import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess } from '../_util';

const accountList = (() => {
  const result: any[] = [];
  for (let index = 0; index < 20; index++) {
    result.push({
      id: `${index}`,
      username: '@first',
      // email: '@email',
      // nickname: '@cname()',
      roles: [
        {
          name: '超级管理员',
          id: 1,
        },
        {
          name: '管理员',
          id: 2,
        },
      ],
      createTime: '@datetime',
      remark: '@cword(10,20)',
      // 'dept|0-2': 1,
      'status|1': [0, 1],
    });
  }
  return result;
})();

const roleList = (() => {
  const result: any[] = [];
  for (let index = 0; index < 4; index++) {
    result.push({
      id: index + 1,
      // orderNo: `${index + 1}`,
      roleName: ['超级管理员', '管理员', '文章管理员', '普通用户'][index],
      permCodes: [
        'system',
        'system:account',
        'system:account:edit',
        'system:role',
        'system:role:edit',
        'system:menu',
        'system:menu:edit',
        'exchange',
      ],
      // roleValue: '@first',
      createTime: '@datetime',
      remark: '@cword(10,20)',
      // menu: [['0', '1', '2'], ['0', '1'], ['0', '2'], ['2']][index],
      // 'status|1': ['0', '1'],
    });
  }
  return result;
})();

const deptList = (() => {
  const result: any[] = [];
  for (let index = 0; index < 3; index++) {
    result.push({
      id: `${index}`,
      deptName: ['华东分部', '华南分部', '西北分部'][index],
      orderNo: index + 1,
      createTime: '@datetime',
      remark: '@cword(10,20)',
      'status|1': ['0', '0', '1'],
      children: (() => {
        const children: any[] = [];
        for (let j = 0; j < 4; j++) {
          children.push({
            id: `${index}-${j}`,
            deptName: ['研发部', '市场部', '商务部', '财务部'][j],
            orderNo: j + 1,
            createTime: '@datetime',
            remark: '@cword(10,20)',
            'status|1': ['0', '1'],
            parentDept: `${index}`,
            children: undefined,
          });
        }
        return children;
      })(),
    });
  }
  return result;
})();

const menuList = (() => {
  const items: any[] = [
    {
      id: 1,
      type: 0,
      name: '系统管理',
      icon: 'ion:settings-outline',
      permission: 'system',
      createTime: '@datetime',
      parentId: '',
    },
    {
      id: 2,
      type: 1,
      name: '账号管理',
      icon: '',
      permission: 'system:account',
      createTime: '@datetime',
      parentId: 1,
    },
    {
      id: 3,
      type: 2,
      name: '账号新增、编辑、删除',
      icon: '',
      permission: 'system:account:edit',
      createTime: '@datetime',
      parentId: 2,
    },
    {
      id: 4,
      type: 1,
      name: '角色管理',
      icon: '',
      permission: 'system:role',
      createTime: '@datetime',
      parentId: 1,
    },
    {
      id: 5,
      type: 2,
      name: '角色新增、编辑、删除',
      icon: '',
      permission: 'system:role:edit',
      createTime: '@datetime',
      parentId: 4,
    },
    {
      id: 6,
      type: 1,
      name: '菜单管理',
      icon: '',
      permission: 'system:menu',
      createTime: '@datetime',
      parentId: 1,
    },
    {
      id: 7,
      type: 2,
      name: '菜单新增、编辑、删除',
      icon: '',
      permission: 'system:menu:edit',
      createTime: '@datetime',
      parentId: 6,
    },
    {
      id: 8,
      type: 1,
      name: '兑奖信息查询',
      icon: '',
      permission: 'exchange',
      createTime: '@datetime',
      parentId: '',
    },
    {
      id: 9,
      type: 1,
      name: '库存兑奖信息',
      icon: '',
      permission: 'stock',
      createTime: '@datetime',
      parentId: '',
    },
    {
      id: 10,
      type: 1,
      name: '赛事管理中心',
      icon: '',
      permission: 'match',
      createTime: '@datetime',
      parentId: '',
    },
  ];

  // for (let index = 0; index < 3; index++) {
  //   result.push({
  //     id: `${index}`,
  //     icon: ['ion:layers-outline', 'ion:git-compare-outline', 'ion:tv-outline'][index],
  //     component: 'LAYOUT',
  //     type: '0',
  //     menuName: ['Dashboard', '权限管理', '功能'][index],
  //     permission: '',
  //     orderNo: index + 1,
  //     createTime: '@datetime',
  //     'status|1': ['0', '0', '1'],
  //     children: (() => {
  //       const children: any[] = [];
  //       for (let j = 0; j < 4; j++) {
  //         children.push({
  //           id: `${index}-${j}`,
  //           type: '1',
  //           menuName: ['菜单1', '菜单2', '菜单3', '菜单4'][j],
  //           icon: 'ion:document',
  //           permission: ['menu1:view', 'menu2:add', 'menu3:update', 'menu4:del'][index],
  //           component: [
  //             '/dashboard/welcome/index',
  //             '/dashboard/analysis/index',
  //             '/dashboard/workbench/index',
  //             '/dashboard/test/index',
  //           ][j],
  //           orderNo: j + 1,
  //           createTime: '@datetime',
  //           'status|1': ['0', '1'],
  //           parentMenu: `${index}`,
  //           children: (() => {
  //             const children: any[] = [];
  //             for (let k = 0; k < 4; k++) {
  //               children.push({
  //                 id: `${index}-${j}-${k}`,
  //                 type: '2',
  //                 menuName: '按钮' + (j + 1) + '-' + (k + 1),
  //                 icon: '',
  //                 permission:
  //                   ['menu1:view', 'menu2:add', 'menu3:update', 'menu4:del'][index] +
  //                   ':btn' +
  //                   (k + 1),
  //                 component: [
  //                   '/dashboard/welcome/index',
  //                   '/dashboard/analysis/index',
  //                   '/dashboard/workbench/index',
  //                   '/dashboard/test/index',
  //                 ][j],
  //                 orderNo: j + 1,
  //                 createTime: '@datetime',
  //                 'status|1': ['0', '1'],
  //                 parentMenu: `${index}-${j}`,
  //                 children: undefined,
  //               });
  //             }
  //             return children;
  //           })(),
  //         });
  //       }
  //       return children;
  //     })(),
  //   });
  // }
  return { items };
})();

export default [
  {
    url: '/basic-api/system.System/getAccountList',
    timeout: 100,
    method: 'post',
    response: ({ body }) => {
      const { page = 1, pageSize = 20 } = body;
      return resultPageSuccess(page, pageSize, accountList);
    },
  },
  {
    url: '/basic-api/system.System/getRoleListByPage',
    timeout: 100,
    method: 'post',
    response: ({ body }) => {
      const { page = 1, pageSize = 20 } = body;
      return resultPageSuccess(page, pageSize, roleList);
    },
  },
  {
    url: '/basic-api/system.System/getAllRoleList',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess(roleList);
    },
  },
  {
    url: '/basic-api/system.System/getMenuList',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess(menuList);
    },
  },
] as MockMethod[];
