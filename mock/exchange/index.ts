import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess } from '../_util';

export default [
  {
    url: '/basic-api/activity.Activity/getObsToken',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess({ token: 'token' });
    },
  },
  {
    url: '/basic-api/activity.Activity/uploadUseImg',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess({});
    },
  },
  {
    url: '/basic-api/manager.Manager/getLists',
    timeout: 100,
    method: 'post',
    response: ({ body }) => {
      const { page = 1, pageSize = 10 } = body;
      return resultSuccess({
        list: Array(pageSize)
          .fill(0)
          .map((_, i) => ({
            id: i + 1 + (page - 1) * 10,
            useImg:
              i % 3 === 0 ? 'https://obs-cdn.52tt.com/tt/fe-moss/20240410163047_58366109.jpg' : '',
            isPush: i % 2 === 0,
            detail: {
              type: '王者荣耀',
              describe: `兑换明细${i + 1}`,
              ttid: 'yjwj-wjde',
              cdkey: 'cdkey123123',
              winningTime: '2021-04-10 16:30:47',
              isUse: i % 2 === 0,
              useTime: '2021-04-10 16:30:47',
              gameId: 'yjwj-wjdegameId',
              gameName: 'yjwj-wjdegameName',
              gameImg: 'https://obs-cdn.52tt.com/tt/fe-moss/20240410163047_58366109.jpg',
              name: '真实姓名',
              phone: '13800138000',
              alipay: i % 7 === 0 ? '13800138000' : '',
              wechat: i % 3 === 0 ? '13822228888' : '',
              os: i % 2 === 0 ? 'ios' : 'android',
              area: i % 2 === 0 ? 'qq' : 'weixin',
              hero: '王者英雄',
              gameHomeImg: 'https://obs-cdn.52tt.com/tt/fe-moss/20240410163047_58366109.jpg',
              gameWishImg: 'https://obs-cdn.52tt.com/tt/fe-moss/20240410163047_58366109.jpg',
            },
          })),
        total: 100,
      });
    },
  },
] as MockMethod[];
