# ImgPreview 图片预览组件

一个功能完整的图片预览和上传组件，支持图片展示、预览、上传、删除等功能。

## 功能特性

- ✅ 图片展示和预览
- ✅ 图片上传（支持多选）
- ✅ 图片删除
- ✅ 加载状态显示
- ✅ 错误状态处理
- ✅ 文件大小和类型验证
- ✅ 自定义上传函数
- ✅ 响应式设计
- ✅ 键盘导航支持

## 基础用法

```vue
<template>
  <div>
    <ImgPreview 
      v-model="imageList"
      :max-count="5"
      :max-size="10"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImgPreview from '@/components/ImgPerview/index.vue'

const imageList = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg'
])

function handleUploadSuccess({ file, result }) {
  console.log('上传成功:', file.name, result)
}

function handleUploadError({ file, error }) {
  console.error('上传失败:', file.name, error)
}
</script>
```

## 只读模式

```vue
<template>
  <ImgPreview 
    v-model="imageList"
    :readonly="true"
    :show-add="false"
    :show-delete="false"
  />
</template>
```

## 自定义上传

```vue
<template>
  <ImgPreview 
    v-model="imageList"
    :custom-upload="customUploadFunction"
    :before-upload="beforeUploadCheck"
  />
</template>

<script setup>
// 自定义上传函数
async function customUploadFunction(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  })
  
  const result = await response.json()
  return result.url // 返回图片URL
}

// 上传前检查
function beforeUploadCheck(file) {
  if (file.size > 5 * 1024 * 1024) {
    alert('文件大小不能超过5MB')
    return false
  }
  return true
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 图片列表 | Array | [] |
| maxCount | 最大上传数量 | Number | 9 |
| maxSize | 最大文件大小(MB) | Number | 10 |
| accept | 接受的文件类型 | String | 'image/*' |
| multiple | 是否支持多选 | Boolean | true |
| showAdd | 是否显示添加按钮 | Boolean | true |
| showDelete | 是否显示删除按钮 | Boolean | true |
| readonly | 是否只读模式 | Boolean | false |
| previewTitle | 预览弹窗标题 | String | '图片预览' |
| beforeUpload | 上传前的钩子函数 | Function | null |
| customUpload | 自定义上传函数 | Function | null |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 图片列表更新 | (imageList: Array) |
| add | 点击添加按钮 | - |
| delete | 删除图片 | ({ index: number, item: Object }) |
| preview | 预览图片 | ({ index: number, url: string }) |
| upload-success | 上传成功 | ({ file: File, result: any }) |
| upload-error | 上传失败 | ({ file: File, error: Error }) |

## 图片数据格式

支持两种格式的图片数据：

### 字符串格式
```javascript
const imageList = [
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg'
]
```

### 对象格式
```javascript
const imageList = [
  {
    url: 'https://example.com/image1.jpg',
    alt: '图片描述',
    loading: false,
    error: false
  }
]
```

## 样式定制

组件使用 Less 编写样式，支持通过 CSS 变量或深度选择器进行定制：

```less
// 自定义图片项大小
.img-preview-container {
  .img-item,
  .add-btn {
    width: 100px;
    height: 100px;
  }
}

// 自定义预览弹窗样式
:deep(.img-preview-modal) {
  .ant-modal-body {
    background: #f5f5f5;
  }
}
```

## 注意事项

1. 组件依赖 `ant-design-vue` 的 Modal 组件
2. 默认上传函数仅用于演示，生产环境需要实现 `customUpload` 函数
3. 图片预览使用浏览器原生功能，支持缩放和拖拽
4. 组件会自动处理图片加载失败的情况
5. 支持键盘导航（左右箭头键切换图片）

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79
