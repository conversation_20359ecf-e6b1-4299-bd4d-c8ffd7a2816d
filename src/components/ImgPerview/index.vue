<!-- src/components/ImgPerview/index.vue -->
<template>
  <div class="img-preview-container">
    <!-- 图片展示区域 -->
    <div
      v-for="(img, index) in imageList"
      :key="index"
      class="img-item"
      @click="handlePreview(index)"
    >
      <img
        :src="getImageUrl(img)"
        :alt="getImageAlt(img, index)"
        class="preview-img"
        :class="{ 'img-error': img.error }"
        @error="handleImageError(index)"
        @load="handleImageLoad(index)"
      />
      <div v-if="img.loading" class="img-loading">
        <div class="loading-spinner"></div>
      </div>
      <div v-if="img.error" class="img-error-mask">
        <div class="error-icon">❌</div>
        <span>加载失败</span>
      </div>
      <!-- 删除按钮 -->
      <div v-if="showDelete && !readonly" class="delete-btn" @click.stop="handleDelete(index)">
        ✕
      </div>
    </div>

    <!-- 添加按钮 -->
    <div
      v-if="showAdd && !readonly && imageList.length < maxCount"
      class="add-btn"
      @click="handleAdd"
    >
      <div class="add-icon">+</div>
      <span>添加图片</span>
    </div>

    <!-- 图片预览弹窗 -->
    <Modal
      :open="showPreview"
      :title="previewTitle"
      :footer="null"
      :width="800"
      @cancel="handlePreviewClose"
      class="img-preview-modal"
    >
      <div class="preview-content">
        <div class="preview-header" v-if="validImages.length > 1">
          <span class="preview-index">{{ currentIndex + 1 }} / {{ validImages.length }}</span>
          <div class="preview-actions">
            <button @click="handlePrevious" :disabled="currentIndex === 0">上一张</button>
            <button @click="handleNext" :disabled="currentIndex === validImages.length - 1"
              >下一张</button
            >
          </div>
        </div>
        <div class="preview-image-container">
          <img
            :src="currentPreviewImage"
            :alt="currentPreviewAlt"
            class="preview-image"
            @error="handlePreviewImageError"
          />
        </div>
      </div>
    </Modal>

    <!-- 文件上传 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { Modal } from 'ant-design-vue';

  // 组件属性定义
  const props = defineProps({
    // 图片列表
    modelValue: {
      type: Array,
      default: () => [],
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 9,
    },
    // 最大文件大小(MB)
    maxSize: {
      type: Number,
      default: 10,
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: 'image/*',
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 是否显示添加按钮
    showAdd: {
      type: Boolean,
      default: true,
    },
    // 是否显示删除按钮
    showDelete: {
      type: Boolean,
      default: true,
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: true,
    },
    // 预览弹窗标题
    previewTitle: {
      type: String,
      default: '图片预览',
    },
    // 上传前的钩子函数
    beforeUpload: {
      type: Function,
      default: null,
    },
    // 自定义上传函数
    customUpload: {
      type: Function,
      default: null,
    },
  });

  // 事件定义
  const emit = defineEmits([
    'update:modelValue',
    'add',
    'delete',
    'preview',
    'upload-success',
    'upload-error',
  ]);

  // 响应式数据
  const showPreview = ref(false);
  const currentIndex = ref(0);
  const fileInputRef = ref(null);

  // 图片列表处理
  const imageList = computed({
    get() {
      return props.modelValue.map((item) => {
        if (typeof item === 'string') {
          return { url: item, loading: false, error: false };
        }
        return { loading: false, error: false, ...item };
      });
    },
    set(value) {
      emit(
        'update:modelValue',
        value.map((item) => item.url || item),
      );
    },
  });

  // 有效图片列表（用于预览）
  const validImages = computed(() => {
    return imageList.value.filter((img) => img.url && !img.error);
  });

  // 当前预览图片
  const currentPreviewImage = computed(() => {
    return validImages.value[currentIndex.value]?.url || '';
  });

  // 当前预览图片描述
  const currentPreviewAlt = computed(() => {
    return validImages.value[currentIndex.value]?.alt || `图片${currentIndex.value + 1}`;
  });

  // 获取图片URL
  function getImageUrl(img) {
    return img.url || img;
  }

  // 获取图片描述
  function getImageAlt(img, index) {
    return img.alt || `图片${index + 1}`;
  }

  // 处理图片预览
  function handlePreview(index) {
    if (imageList.value[index].error) return;

    const validIndex = validImages.value.findIndex(
      (item) => item.url === imageList.value[index].url,
    );
    if (validIndex !== -1) {
      currentIndex.value = validIndex;
      showPreview.value = true;
      emit('preview', { index, url: imageList.value[index].url });
    }
  }

  // 处理预览关闭
  function handlePreviewClose() {
    showPreview.value = false;
  }

  // 处理上一张
  function handlePrevious() {
    if (currentIndex.value > 0) {
      currentIndex.value--;
    }
  }

  // 处理下一张
  function handleNext() {
    if (currentIndex.value < validImages.value.length - 1) {
      currentIndex.value++;
    }
  }

  // 处理预览图片加载错误
  function handlePreviewImageError() {
    console.error('预览图片加载失败:', currentPreviewImage.value);
  }

  // 处理图片加载成功
  function handleImageLoad(index) {
    const newList = [...imageList.value];
    newList[index] = { ...newList[index], loading: false, error: false };
    updateImageList(newList);
  }

  // 处理图片加载失败
  function handleImageError(index) {
    const newList = [...imageList.value];
    newList[index] = { ...newList[index], loading: false, error: true };
    updateImageList(newList);
  }

  // 处理删除图片
  async function handleDelete(index) {
    try {
      const result = await new Promise((resolve) => {
        const confirmed = confirm('确定要删除这张图片吗？');
        resolve(confirmed);
      });

      if (result) {
        const newList = [...imageList.value];
        const deletedItem = newList.splice(index, 1)[0];
        updateImageList(newList);
        emit('delete', { index, item: deletedItem });
      }
    } catch (error) {
      console.error('删除图片失败:', error);
    }
  }

  // 处理添加图片
  function handleAdd() {
    if (fileInputRef.value) {
      fileInputRef.value.click();
    }
    emit('add');
  }

  // 处理文件选择
  async function handleFileChange(event) {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // 检查数量限制
    if (imageList.value.length + files.length > props.maxCount) {
      alert(`最多只能上传 ${props.maxCount} 张图片`);
      return;
    }

    // 处理每个文件
    for (const file of files) {
      await processFile(file);
    }

    // 清空文件输入
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }
  }

  // 处理单个文件
  async function processFile(file) {
    // 检查文件大小
    if (file.size > props.maxSize * 1024 * 1024) {
      alert(`文件大小不能超过 ${props.maxSize}MB`);
      return;
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      alert('只能上传图片文件');
      return;
    }

    // 执行上传前钩子
    if (props.beforeUpload) {
      const result = await props.beforeUpload(file);
      if (result === false) return;
    }

    // 创建临时图片项
    const tempItem = {
      url: URL.createObjectURL(file),
      loading: true,
      error: false,
      file,
      alt: file.name,
    };

    // 添加到列表
    const newList = [...imageList.value, tempItem];
    updateImageList(newList);

    try {
      let uploadResult;
      if (props.customUpload) {
        // 使用自定义上传函数
        uploadResult = await props.customUpload(file);
      } else {
        // 默认上传逻辑（这里需要根据项目实际情况实现）
        uploadResult = await defaultUpload(file);
      }

      // 更新上传成功的图片
      const finalList = newList.map((item) => {
        if (item.file === file) {
          return {
            url: uploadResult.url || uploadResult,
            loading: false,
            error: false,
            alt: item.alt,
          };
        }
        return item;
      });
      updateImageList(finalList);
      emit('upload-success', { file, result: uploadResult });
    } catch (error) {
      console.error('上传失败:', error);

      // 更新上传失败的图片
      const errorList = newList.map((item) => {
        if (item.file === file) {
          return { ...item, loading: false, error: true };
        }
        return item;
      });
      updateImageList(errorList);
      emit('upload-error', { file, error });
    }
  }

  // 默认上传函数（需要根据项目实际情况实现）
  async function defaultUpload(file) {
    // 这里应该实现实际的上传逻辑
    // 返回上传后的图片URL
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟上传成功
        resolve({
          url: URL.createObjectURL(file),
        });
      }, 1000);
    });
  }

  // 更新图片列表
  function updateImageList(newList) {
    const urls = newList.map((item) => item.url || item);
    emit('update:modelValue', urls);
  }
</script>

<style lang="less" scoped>
  .img-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .img-item {
      position: relative;
      width: 80px;
      height: 80px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .preview-img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block;

        &.img-error {
          opacity: 0.5;
        }
      }

      .img-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);

        .loading-spinner {
          width: 20px;
          height: 20px;
          border: 2px solid #f3f3f3;
          border-top: 2px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .img-error-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        font-size: 12px;

        .error-icon {
          font-size: 16px;
          margin-bottom: 4px;
        }
      }

      .delete-btn {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 20px;
        height: 20px;
        background: #ff4d4f;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: #ff7875;
          transform: scale(1.1);
        }
      }
    }

    .add-btn {
      width: 80px;
      height: 80px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      color: #666;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      .add-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }

      span {
        font-size: 12px;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 预览弹窗样式
  :deep(.img-preview-modal) {
    .ant-modal-body {
      padding: 0;
    }

    .preview-content {
      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        .preview-index {
          font-size: 14px;
          color: #666;
        }

        .preview-actions {
          display: flex;
          gap: 8px;

          button {
            padding: 4px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;

            &:hover:not(:disabled) {
              border-color: #1890ff;
              color: #1890ff;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }

      .preview-image-container {
        padding: 20px;
        text-align: center;
        max-height: 60vh;
        overflow: auto;
      }
    }
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  // 响应式设计
  @media (max-width: 768px) {
    .img-preview-container {
      .img-item,
      .add-btn {
        width: 60px;
        height: 60px;
      }
    }

    :deep(.img-preview-modal) {
      .ant-modal {
        margin: 0;
        max-width: 100vw;
        height: 100vh;
      }

      .preview-content {
        .preview-image-container {
          max-height: calc(100vh - 120px);
        }
      }
    }
  }
</style>
