import url from '@/utils/url';
import { createServiceApi, createRequest, createCustomRequest } from './httpUtils';
import {
  AddActivityParams,
  GetActivityListParams,
  GetAuditOrderListReq,
  GetAuditOrderReq,
  ManageRewardParams,
  GetApiLogReq,
  BatchAuditOrderReq,
  BatchAuditOrderRes,
  ManageActivityReq,
} from './model/pcActivityModel';

const services = {
  activity: {
    app: 'master-invitation-code',
    baseUrl: url.node_activity,
    prefix: '/withdraw_audit.WithdrawAudit/',
  },
  match: {
    app: 'match-operate-center',
    baseUrl: url.node_common,
    prefix: '/activity.Activity/',
  },
};
enum Api {
  getActList = 'getActList', // 获取活动列表
  createActivity = 'createActivity', // 新增活动
  getAuditOrderList = 'getAuditOrderList', // 审核订单列表
  auditOrder = 'auditOrder', // 审核订单
  batchAuditOrder = 'batchAuditOrder', // 批量审核订单
  batchGiveUserCoin = 'batchGiveUserCoin', // 批量发放用户碎片
  batchRecycleUserCoin = 'batchRecycleUserCoin', // 批量回收用户碎片
  getApiLog = 'getApiLog', //获取操作记录
}

const matchApis = createServiceApi(services.match);
const mangeMatchApis = createServiceApi(services.activity, '/admin.Admin/');

// 活动
export const createActivity = createRequest<AddActivityParams>(matchApis, Api.createActivity);
export const getActList = createRequest<GetActivityListParams>(matchApis, Api.getActList);

// 操作记录
export const getApiLogApi = createRequest<GetApiLogReq>(matchApis, Api.getApiLog);

// 碎片操作
export const batchGiveUserCoin = createRequest<ManageRewardParams>(
  mangeMatchApis,
  Api.batchGiveUserCoin,
);
export const batchRecycleUserCoin = createRequest<ManageRewardParams>(
  mangeMatchApis,
  Api.batchRecycleUserCoin,
);

// 审核订单列表 (动态匹配单个项目)
export const getAuditOrderListApi = (data: GetAuditOrderListReq) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: `/withdraw_audit.WithdrawAudit/`,
    },
    Api.getAuditOrderList,
  )(data);
};
// 审核订单
export const auditOrderApi = (data: GetAuditOrderReq) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: `/withdraw_audit.WithdrawAudit/`,
    },
    Api.auditOrder,
  )(data);
};

// 批量审核订单
export const batchAuditOrderApi = (data: BatchAuditOrderReq) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: `/withdraw_audit.WithdrawAudit/`,
    },
    Api.batchAuditOrder,
  )(data);
};

// 发放 | 回收 | 编辑等操作统一这样动态实现
export const manageActivityApi = (data: ManageActivityReq) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: '',
    },
    data?.url || '',
  )(data);
};
