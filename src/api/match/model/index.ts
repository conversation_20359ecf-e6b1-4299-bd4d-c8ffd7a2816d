export interface GetActivityListParams {
  skip?: number;
  limit?: number;
  search?: string;
}
export interface GrantRewardParams {
  activityId?: string;
  uidList?: string[];
  num?: number;
  reason?: number;
  recordType?: string;
}
export interface RecordParams {
  activityId?: string;
  startTime?: number;
  endTime?: number;
  recordType?: string;
  operateType?: string;
  search?: string;
}

export interface QqGroupParams {
  activityId?: string;
  qq?: string;
  kol_qq?: string;
}

export interface QqGroupResult {
  list?: QqGroupParams;
  total?: number;
}
export interface RecordModel {
  activityId?: string;
  uid?: string;
  ttid?: string;
  recordType?: string;
  operateType?: string;
  num?: string | number;
  reason?: string | number;
  username: string;
  createdAt?: string;
}
export interface RecordResult {
  list?: RecordParams;
  total?: number;
}

export interface ReasonModel {
  label: string;
  value: number | string;
  color?: string; // 颜色
}
export interface QqGroupRecordParams {
  activityId?: string;
  skip?: number;
  limit?: number;
}
export interface ActivityModel {
  activityId?: string;
  name?: string;
  createdAt?: string;
}
export interface CompetitionRuleParams {
  game_id: number | string;
  page?: number;
  size?: number;
  start_time_range?: [number, number];
}
export interface CompetitionRuleConfigParams {
  join_end_min?: string;
  join_start_min?: string;
  enter_saibao_start_min?: string;
  enter_saibao_end_min?: string;
  max_team_number?: string;
}
export interface CreateCompetitionRuleParams {
  name?: string;
  game_mode?: number;
  battle_mode?: number;
  bo_num?: number;
  game_start_mode?: number;
  ext_json?: CompetitionRuleConfigParams;
  user_group_rule?: string;
  id?: string;
  game_id?: number;
}
export interface ScheduleParams {
  name: string;
  competition_rule_id?: number;
  game_start_time?: number | string;
  game_end_time?: number | string;
  game_map_name?: string;
  game_zone?: string;
  child_schedule_count?: number;
}
export interface UpdateScheduleListParams {
  schedule_rows: ScheduleParams[];
}
export interface GetScheduleListParams {
  schedule_id: number;
  page?: number;
  size?: number;
}
export interface UpdateChildScheduleListParams {
  child_schedule_count?: number | string;
  schedule_id: number | string;
}
export interface GetGameResultParams {
  child_id: string;
}

export interface CompetitionRuleModel {}

export interface CancelScheduleParams {
  schedule_ids: string[];
}
export interface CompeteData {
  team_a_score: number | string;
  team_b_score: number | string;
  team_a?: object | null;
  team_b?: object | null;
}
export interface BannerModel {
  img?: string;
  url?: string;
  priority?: number;
  displayTime?: string[];
  platform?: number[];
}
export interface BannerParams {
  banner: BannerModel[];
}
export interface whiteWithdrawalParams {
  activityId?: string;
  uidList?: string[];
  is_delete?: boolean;
  recordType?: string;
}
export interface DailyWithdrawLimitParams {
  bountyMoneyLimit?: number | string;
  ticketMoneyLimit?: number | string;
  operator?: string | number;
  activityId?: string;
}
export interface GetApiLogReq {
  activityId?: string; // 活动ID
  url?: string; // 接口url
  startTime?: number | string; // 开始时间戳
  endTime?: number | string; // 结束时间戳
  query?: string; // 查询条件 json string, 对应该接口的参数
  skip?: number; // 跳过多少
  limit?: number; // 取多少
}

export interface ManageActivityReq {
  activityId: string; // 活动ID
  url: string; // 接口url
  [key: string]: any; // 其他随意
}
