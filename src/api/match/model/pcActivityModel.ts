export interface AddActivityParams {
  activityId?: string;
  name?: string;
  tabId?: Number | string;
}
export interface GetActivityListParams {
  page?: number;
  size?: number;
  tabId?: Number | string;
  name?: string;
}
export interface GetAuditOrderListReq {
  ttid?: string;
  auditStatus?: number;
  payStatus?: number;
  masterLevel?: number | string;
  createTime?: Array<number>;
  page?: number;
  size?: number;
  activityId?: string;
}
export interface AuditOrderListParams {
  orderId?: string;
  ttid?: string;
  uid?: number;
  nickname?: string;
  money?: string;
  masterLevel?: number | string;
  payStatus?: number | string;
  auditStatus?: number;
  createTime?: number;
  exchangeCoin?: number;
  currentCoin?: number;
}
export interface GetAuditOrderListRes {
  list?: AuditOrderListParams[];
  total?: number;
}
export interface GetAuditOrderReq {
  orderId?: string;
  auditStatus?: number;
  activityId?: string;
}
export interface BatchAuditOrderReq {
  orderIdList?: Array<string>;
  auditStatus?: number;
  activityId?: string;
}
export interface BatchAuditOrderRes {
  failOrderIdList?: Array<string>;
}
export interface ManageRewardParams {
  uidList?: string[];
  ttidList?: string[];
  coinNum?: number;
  remark?: string;
}
export interface GetApiLogReq {
  activityId?: string; // 活动ID
  url?: string; // 接口url
  startTime?: number | string; // 开始时间戳
  endTime?: number | string; // 结束时间戳
  query?: string; // 查询条件 json string, 对应该接口的参数
  skip?: number; // 跳过多少
  limit?: number; // 取多少
}
export interface GetApiLogInfo {
  activityId?: string;
  url?: string;
  requestBody?: string;
  username?: string;
  createdAt?: string;
}
export interface GetApiLogResp {
  list?: GetApiLogInfo[];
  total?: number;
}
// export interface ActivityOperationInfo {
//   title?: string;
//   operateId?: string;
//   logApiMap?: {
//     [key: string]: string;
//   };
// }

export interface ManageActivityReq {
  activityId: string; // 活动ID
  url: string; // 接口url
  [key: string]: any; // 其他随意
}
