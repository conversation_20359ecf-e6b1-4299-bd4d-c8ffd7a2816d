import { getUrlENV } from '@/utils/env';
import { createAxios } from '@/utils/http/axios';
import { parseUrlQuery, baseDomain } from '@/utils/url';

// 定义服务配置类型
interface ServiceConfig {
  app: string;
  baseUrl: () => Record<string, string>;
  prefix: string;
}

// 定义环境类型
type EnvType = string;

// 获取当前环境
const env: EnvType = parseUrlQuery().env || getUrlENV() || 'prod';

/**
 * 创建服务API实例
 * @param app 服务应用名称
 * @param customPrefix 自定义前缀（可选）
 * @param axiosOptions 额外的Axios配置（可选）
 * @returns Axios实例
 */
export function createServiceApi(config: ServiceConfig, customPrefix?: string) {
  if (!config) {
    throw new Error(`Service "${config}" not found in services configuration`);
  }

  const baseUrl = config.baseUrl()[env] || config.baseUrl()['prod']; // 添加默认环境回退

  return createAxios({
    requestOptions: {
      apiUrl: `${baseUrl}/${config.app}`,
      urlPrefix: customPrefix || config.prefix,
    },
  });
}
export function createCustomRequest(
  { key = 'node_activity', app = '', prefix = '/activity.Activity/' },
  url: string,
) {
  const baseUrl = baseDomain[key]()[env] || baseDomain[key]()['prod'];
  const instance = createAxios({
    requestOptions: {
      apiUrl: `${baseUrl}/${app}`,
      urlPrefix: prefix,
    },
  });
  return (params: any) => {
    return instance.post({
      url,
      params,
    });
  };
}

export function createRequest<T = any>(http: any, url: string) {
  return (params: T) => http.post({ url, params });
}
