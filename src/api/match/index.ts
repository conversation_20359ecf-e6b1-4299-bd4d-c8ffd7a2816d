import url from '@/utils/url';
import { createServiceApi, createRequest, createCustomRequest } from './httpUtils';
import {
  GetActivityListParams,
  GrantRewardParams,
  RecordParams,
  QqGroupParams,
  QqGroupRecordParams,
  CompetitionRuleParams,
  CreateCompetitionRuleParams,
  CancelScheduleParams,
  UpdateScheduleListParams,
  GetScheduleListParams,
  UpdateChildScheduleListParams,
  GetGameResultParams,
  BannerParams,
  whiteWithdrawalParams,
  DailyWithdrawLimitParams,
  GetApiLogReq,
  ManageActivityReq,
} from './model';

const serviceConfigs = {
  matchOperateCenter: {
    app: 'match-operate-center',
    baseUrl: url.node_common,
    prefix: '/matchManager.MatchManager/',
  },
  opManageProxy: {
    app: 'competition-platform-svr',
    baseUrl: url.node_common,
    prefix: '/com_backend.CompetitionBackend/',
  },
};
const actHttp = createServiceApi(serviceConfigs.matchOperateCenter, '/activity.Activity/');
const defHttp = createServiceApi(serviceConfigs.matchOperateCenter);
const appHttp = createServiceApi(serviceConfigs.opManageProxy);

enum Api {
  getActList = 'getActList', // 获取活动列表

  // 旧接口
  grantTicketAndBounty = 'grantTicketAndBounty', // 发放门票和赏金
  recycleTicketAndBounty = 'recycleTicketAndBounty', // 回收门票和赏金
  getTicketAndBountyRecord = 'getTicketAndBountyRecord', // 获取门票和赏金发放/回收记录
  setQqGroup = 'setQqGroup', // 设置QQ群
  getOperateQqLog = 'getOperateQqLog', // 获取操作QQ群日志
  setBanner = 'setBanner', // 设置资源位
  getSetBannerLog = 'getSetBannerLog', // 获取操作资源位日志
  setWhiteWithdrawal = 'setWhiteWithdrawal', // 设置白名单提现
  setWhiteWithdrawalLog = 'setWhiteWithdrawalLog', // 获取白名单提现日志
  setDailyWithdrawLimit = 'setDailyWithdrawLimit', // 设置每日提现金额上限

  // 新接口
  // 获取日志
  getApiLog = 'getApiLog', //获取操作记录

  getCompetitionRuleList = 'getCompetitionRuleList', // 获取赛事规则列表
  createCompetitionRule = 'createCompetitionRule', // 创建赛事规则
  updateCompetitionRule = 'updateCompetitionRule', // 更新赛事规则
  getScheduleList = 'getScheduleList', // 获取赛程列表
  cancelSchedule = 'cancelSchedule', // 下线赛程
  uploadXlsSchedule = 'uploadXlsSchedule', // 上传创建赛程
  getChildScheduleList = 'getChildScheduleList', // 获取子赛程列表
  createChildSchedule = 'createChildSchedule', // 创建子赛程
  getGameResult = 'getGameResult', // 获取比赛结果
  getGameMapList = 'getGameMapList', // 获取地图列表
}

export const getActListApi = createRequest<GetActivityListParams>(actHttp, Api.getActList);

export const grantTicketAndBountyApi = createRequest<GrantRewardParams>(
  defHttp,
  Api.grantTicketAndBounty,
);

export const recycleTicketAndBountyApi = createRequest<GrantRewardParams>(
  defHttp,
  Api.recycleTicketAndBounty,
);

export const getTicketAndBountyRecordApi = createRequest<RecordParams>(
  defHttp,
  Api.getTicketAndBountyRecord,
);

export const setQqGroupApi = createRequest<QqGroupParams>(defHttp, Api.setQqGroup);
export const getOperateQqLogApi = createRequest<QqGroupRecordParams>(defHttp, Api.getOperateQqLog);
export const setBannerApi = createRequest<BannerParams>(defHttp, Api.setBanner);
export const getSetBannerLogApi = createRequest<BannerParams>(defHttp, Api.getSetBannerLog);
export const setWhiteWithdrawalApi = createRequest<whiteWithdrawalParams>(
  defHttp,
  Api.setWhiteWithdrawal,
);

export const setWhiteWithdrawalLogApi = createRequest<whiteWithdrawalParams>(
  defHttp,
  Api.setWhiteWithdrawalLog,
);

// 运营后台迁移
export const getCompetitionRuleListApi = createRequest<CompetitionRuleParams>(
  appHttp,
  Api.getCompetitionRuleList,
);

export const createCompetitionRuleApi = createRequest<CreateCompetitionRuleParams>(
  appHttp,
  Api.createCompetitionRule,
);

export const updateCompetitionRuleApi = createRequest<CreateCompetitionRuleParams>(
  appHttp,
  Api.updateCompetitionRule,
);

export const getScheduleListApi = createRequest<CompetitionRuleParams>(
  appHttp,
  Api.getScheduleList,
);

export const cancelScheduleApi = createRequest<CancelScheduleParams>(appHttp, Api.cancelSchedule);

export const uploadXlsScheduleApi = createRequest<UpdateScheduleListParams>(
  appHttp,
  Api.uploadXlsSchedule,
);

export const getChildScheduleListApi = createRequest<GetScheduleListParams>(
  appHttp,
  Api.getChildScheduleList,
);

export const createChildScheduleApi = createRequest<UpdateChildScheduleListParams>(
  appHttp,
  Api.createChildSchedule,
);

export const getGameResultApi = createRequest<GetGameResultParams>(appHttp, Api.getGameResult);
export const getGameMapListApi = createRequest<CompetitionRuleParams>(appHttp, Api.getGameMapList);

// 每日设置提现金额上限
export const setDailyWithdrawLimitApi = (data: DailyWithdrawLimitParams) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: '/admin.Admin/',
    },
    Api.setDailyWithdrawLimit,
  )(data);
};

// 发放 | 回收 | 编辑等操作统一这样动态实现
export const manageActivityApi = (data: ManageActivityReq) => {
  return createCustomRequest(
    {
      key: 'node_activity',
      app: data.activityId || 'master-invitation-code',
      prefix: '',
    },
    data?.url || '',
  )(data);
};

// 操作记录
export const getApiLogApi = createRequest<GetApiLogReq>(actHttp, Api.getApiLog);
