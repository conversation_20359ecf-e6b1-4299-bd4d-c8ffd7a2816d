import url from '@/utils/url';
import {
  AccountParams,
  MenuParams,
  RoleParams,
  RolePageParams,
  MenuListGetResultModel,
  AccountListGetResultModel,
  RolePageListGetResultModel,
  RoleListGetResultModel,
  EditMenuParams,
  EditRoleParams,
  EditAccountParams,
} from './model/systemModel';
import { createAxios } from '@/utils/http/axios';
import { getUrlENV } from '@/utils/env';

const nodePath = 'operation-platform';
const env = getUrlENV();

const defHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common('//testing-tt-web-tc.ttyuyin.com/common-testing')[env]}${/* env === 'dev' ? '' :  */ `/${nodePath}`}`,
    // apiUrl: `${url.node_common()['testing']}/${nodePath}`,
    urlPrefix: '/system.System/',
  },
});

enum Api {
  AccountList = 'getAccountList',
  addAccount = 'addAccount',
  delAccount = 'delAccount',
  MenuList = 'getMenuList',
  addMenu = 'addMenu',
  delMenu = 'delMenu',
  RolePageList = 'getRoleListByPage',
  GetAllRoleList = 'getAllRoleList',
  addRole = 'addRole',
  delRole = 'delRole',
}

export const getAccountList = (params: AccountParams) =>
  defHttp.post<AccountListGetResultModel>({ url: Api.AccountList, params });

export const addAccount = (params?: EditAccountParams) =>
  defHttp.post<any>({ url: Api.addAccount, params });

export const delAccount = (params?: { id: string }) =>
  defHttp.post<any>({ url: Api.delAccount, params });

export const getMenuList = (params?: MenuParams) =>
  defHttp.post<MenuListGetResultModel>({ url: Api.MenuList, params });

export const addMenu = (params?: EditMenuParams) => defHttp.post<any>({ url: Api.addMenu, params });

export const delMenu = (params?: { id: string }) => defHttp.post<any>({ url: Api.delMenu, params });

export const getRoleListByPage = (params?: RolePageParams) =>
  defHttp.post<RolePageListGetResultModel>({ url: Api.RolePageList, params });

export const getAllRoleList = (params?: RoleParams) =>
  defHttp.post<RoleListGetResultModel>({ url: Api.GetAllRoleList, params });

export const addRole = (params?: EditRoleParams) => defHttp.post<any>({ url: Api.addRole, params });

export const delRole = (params?: { id: string }) => defHttp.post<any>({ url: Api.delRole, params });
