import { BasicPageParams, BasicFetchResult } from '@/api/model/baseModel';
import { PermissionEnum } from '@/enums/permissionEnum';

export type AccountParams = BasicPageParams & {
  account?: string;
  // nickname?: string;
  [key: string]: any;
};

export type EditAccountParams = {
  id?: string;
  username?: string;
  roles?: { id: string; name: string }[];
  remark?: string;
  status?: number;
  password?: string;
};

export type RoleParams = {
  roleName?: string;
  // status?: string;
};

export type EditRoleParams = {
  id?: string;
  roleName?: string;
  permCodes?: PermissionEnum[];
  remark?: string;
};

export type RolePageParams = BasicPageParams & RoleParams;

export type DeptParams = {
  deptName?: string;
  status?: string;
};

export type MenuParams = {
  menuName?: string;
  // status?: string;
};

export type EditMenuParams = {
  id?: string;
  type?: number;
  name?: string;
  icon?: string;
  parentId?: string;
  permission?: string;
};

export interface AccountListItem {
  id: string;
  username: string;
  // email: string;
  // nickname: string;
  roles: { name: string; id: string }[];
  createTime: string;
  remark: string;
  status: number;
}

export interface DeptListItem {
  id: string;
  orderNo: string;
  createTime: string;
  remark: string;
  status: number;
}

export interface MenuListItem {
  id: string;
  type: number;
  name: string;
  icon: string;
  permission: string;
  createTime: string;
  parentId: string;
  // status: number;
  // component: string;
}

export interface RoleListItem {
  id: string;
  roleName: string;
  permCodes: PermissionEnum[];
  // status: number;
  // orderNo: string;
  createTime: string;
  remark: string;
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem>;

export type DeptListGetResultModel = BasicFetchResult<DeptListItem>;

export interface MenuListGetResultModel {
  items: MenuListItem[];
}

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleListGetResultModel = RoleListItem[];
