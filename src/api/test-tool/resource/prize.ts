import { rewardViewHttp, sendPrizeHttp } from '@/utils/http/axios';
import {
  UsersPrizesParams,
  UsersPrizesModel,
  GuildPrizesParams,
  GuildPrizesModel,
  WhiteListParams,
  WhiteListModel,
  AddWhiteListParams,
} from './model/prizeModel';

enum Api {
  usersPrizes = '/users/prizes',
  guildPrizes = '/guild/${guildId}/prizes',
  prizesManager = '/manager.Manager/',
}

export function getUsersPrizes(params: UsersPrizesParams) {
  return rewardViewHttp.get<UsersPrizesModel>(
    {
      url: Api.usersPrizes,
      headers: { 'x-qw-traffic-mark': 'staging' },
      params,
    },
    { errorMessageMode: 'none' },
  );
}

export function getGuildPrizes(params: GuildPrizesParams) {
  const { guildId, type } = params;
  return rewardViewHttp.get<GuildPrizesModel>(
    {
      url: Api.guildPrizes.replace('${guildId}', guildId),
      headers: { 'x-qw-traffic-mark': 'staging' },
      params: {
        uid: guildId,
        type,
      },
    },
    { errorMessageMode: 'none' },
  );
}

export function getWhiteList(params: WhiteListParams) {
  const { matchId } = params;
  return sendPrizeHttp
    .post<WhiteListModel>(
      {
        url: Api.prizesManager + 'getWhiteList',
        headers: { 'x-qw-traffic-mark': 'staging' },
        params: {
          matchId: matchId || '',
        },
      },
      { errorMessageMode: 'none' },
    )
    .then((res) => ({ ...res, list: res.list || [] }));
}

export function addWihtList(params: AddWhiteListParams) {
  const { uidList } = params;
  const arrUids = `${uidList}`.split(',');
  return sendPrizeHttp.post(
    {
      url: Api.prizesManager + 'addWihtList',
      headers: { 'x-qw-traffic-mark': 'staging' },
      params: {
        uidList: arrUids || [],
      },
    },
    { errorMessageMode: 'none' },
  );
}

export function removeWhiteUser(params) {
  const { uid } = params;
  return sendPrizeHttp.post(
    {
      url: Api.prizesManager + 'removeWhiteUser',
      headers: { 'x-qw-traffic-mark': 'staging' },
      params: {
        uid,
      },
    },
    { errorMessageMode: 'none' },
  );
}
