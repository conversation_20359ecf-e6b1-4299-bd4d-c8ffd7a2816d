export interface UsersPrizesParams {
  uid: string;
  type: string;
}

export interface UsersPrizesModelItem {
  uid: number;
  nickname: string;
  id: number;
  name: string;
  type: string;
  time: string;
  expireTime: string;
  customText: string;
  cpInfo: {
    uid: number;
    phone: string;
    username: string;
    alias: string;
    nickname: string;
    sex: string;
    signature: string;
    password: string;
    verify: true;
    current_guild_id: number;
    last_quit_guild_type: number;
    registered_at: number;
    question: number;
    last_quit_guild_id: number;
    last_quit_guild_time: number;
    who_invite_uid: number;
    invite_code: string;
    status: number;
    user_type: number;
    fromid: string;
    source: number;
    last_login_at: number;
    password_set: true;
    prefix_valid: number;
    original_nickname: string;
    guild_prefix: string;
    is_unregister: false;
    is_clear_local_account: false;
    is_noble: false;
  };
  typeHuman: string;
}

export interface UsersPrizesModel {
  rewards: UsersPrizesModelItem[];
}

export interface GuildPrizesParams {
  guildId: string;
  type: string;
}

export interface GuildPrizesModel {
  rewards: any[];
}

export interface WhiteListParams {
  matchId: string;
}

export interface WhiteListModel {
  list: any[];
}

export interface AddWhiteListParams {
  uidList: any[];
}
