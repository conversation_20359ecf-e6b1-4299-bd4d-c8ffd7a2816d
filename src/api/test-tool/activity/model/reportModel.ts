export interface CommonReqParams {
  activityId: string;
  customDomain?: string;
  headers?: { 'x-qw-traffic-mark': string } | Record<string, string>;
}

export interface CheckSecretParams extends CommonReqParams {
  secret: string;
}

export interface GetReportFnResp {
  list: FnItem[];
}

export interface GetReportListResp {
  list: GetReportListItem[];
}
export interface GetReportListItem {
  date: string;
  fileName: string;
  name: string;
  reportName: string;
  obsUrl: string;
  source: number;
  status: number;
}
export interface ReportParams extends CommonReqParams {
  fn: string;
  pwd: string;
}

export interface FnItem {
  fn: string;
  name: string;
}
