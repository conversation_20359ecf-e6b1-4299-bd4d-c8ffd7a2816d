import { CommonReqParams } from './reportModel';

export interface RedisSelectItem {
  label: string;
  value: string;
}
export interface EnvConfTableItem {
  canEdit: boolean;
  isFn: boolean;
  key: string;
  keyName: string;
  value: string;
}
export interface CatEnvResp {
  list: EnvConfTableItem[];
  redisKeyList: RedisSelectItem[];
}

export interface SetEnvParams extends CommonReqParams {
  key: string;
  value: string;
}

export interface DelKeyParams extends CommonReqParams {
  keyList: string[];
}
