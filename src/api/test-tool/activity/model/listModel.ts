export interface GetProjectListParams {
  activity_id?: string;
  activity_name?: string;
  page?: number;
  size?: number;
}

export interface ProjectItemModel {
  // 活动id
  activity_id: string;
  // 活动名称
  activity_name: string;
  // 贡献者
  contributors: string[];
  // 展示图标
  show_icon: string;
  // 本地ip
  local_ip_port: string;
  // 更新时间
  updateTime: number;
  // 创建时间
  createTime: number;
  // 拓展功能列表
  extends_func_list: string[];
}

export interface ProjectListModel {
  list: ProjectItemModel[];
  total: number;
  page: number;
  size: number;
}

export interface SearchInfo {
  activity_id?: string;
  activity_name?: string;
}

export interface GetProjectByIdParams {
  activity_id: string;
}
