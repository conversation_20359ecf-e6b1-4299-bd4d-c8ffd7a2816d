import { CommonReqParams } from './reportModel';

export interface ItemLuckyDraw {
  attachId: string;
  consume: number;
  freeNum: string;
  id: string;
  itemNum: string;
  itemNumResetFreq: string;
  lockNum: boolean;
  prize: { id: string; num: number };
  probability: number;
  userNum: string;
  userNumResetFreq: string;

  hitNum?: number; // 本轮中奖次数
  realRate?: string; // 实际概率
}
interface PrOfItem {
  [key: string]: number;
}

export interface GetLuckyDrawNamespaceListResp {
  namespaceList: string[];
  namespaceDesc: Record<string, string>;
  redemptionNamespaceList: string[];
  redemptionNamespaceDesc: Record<string, string>;
}

export interface GetLuckyDrawPoolParams extends CommonReqParams {
  luckyDrawNamespace: string;
  uid: number;
}
export interface GetLuckyDrawPoolResp {
  list: ItemLuckyDraw[];
  prOfItem: PrOfItem;
  serverTime: number;
  debugRewardId: string;
  debugNum: number;
}

export interface MockLuckyDrawParams extends CommonReqParams {
  luckyDrawNamespace: string;
  uid: number;
  count: number;
  isRandomUid: boolean;
}
export interface MockLuckyDrawResp {
  originItems: ItemLuckyDraw[];
  rewardObj: { [key: string]: number };
}

export interface ModifyLuckyDrawPoolParams extends CommonReqParams {
  luckyDrawNamespace: string; // 幸运转盘命名空间
  id: string; // 物品ID
  incrementItemNum?: number; // 修改物品库存数量，正数为增加，负数为减少
  uid?: number; // 有uid代表修改对应的用户库存
  incrementUserNum?: number; // 修改用户库存数量，正数为增加，负数为减少
}
export interface ModifyLuckyDrawPoolResp {
  luckyDrawNamespace: string; // 幸运转盘命名空间
  uid: number; // 有uid代表修改对应的用户库存
  id: string; // 物品ID
  realIncrementNum: number; // 实际修改的库存数量，正数为增加，负数为减少
  operateDesc: string; // 操作描述
}

export interface SetNextLuckyDrawParams extends CommonReqParams {
  debugNamespace: string;
  debugRewardId: string;
  uid: number;
  debugNum: number;
}

export interface SetNextLuckyDrawResp {}

export interface GetRedemptionPoolParams extends CommonReqParams {
  redemptionNamespace: string;
  uid: number;
}
export interface GetRedemptionPoolResp {
  list: ItemLuckyDraw[];
  serverTime: number;
}
