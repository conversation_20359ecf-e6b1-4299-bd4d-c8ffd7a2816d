import { toolhubHttp } from '@/utils/http/axios';
import type {
  GetProjectListParams,
  ProjectListModel,
  GetProjectByIdParams,
  ProjectItemModel,
} from './model/listModel';

enum Api {
  GetProjectList = '/project.Project/getProjectList',
  GetProjectById = '/project.Project/getProjectById',
}

// 获取项目列表
export function getProjectListApi(params?: GetProjectListParams) {
  return toolhubHttp.post<ProjectListModel>({
    url: Api.GetProjectList,
    params,
  });
}

// 根据项目id获取项目信息
export function getProjectByIdApi(params: GetProjectByIdParams) {
  return toolhubHttp.post<ProjectItemModel>({
    url: Api.GetProjectById,
    params,
  });
}
