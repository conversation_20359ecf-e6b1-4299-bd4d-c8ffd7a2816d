import { activityHttp } from '@/utils/http/axios';
import {
  CheckSecretParams,
  CommonReqParams,
  GetReportFnResp,
  GetReportListResp,
  ReportParams,
} from './model/reportModel';
import { CatEnvResp, SetEnvParams, DelKeyParams } from './model/envConfModel';

enum Api {
  GetReportFn = '/manager.Manager/getReportFn',
  GetReportList = '/manager.Manager/getReportList',
  CheckSecret = '/manager.Manager/checkSecret',
  Report = '/manager.Manager/report',
  ///
  CatEnv = '/manager.Manager/catEnv',
  SetEnv = '/manager.Manager/setEnv',
  DelKey = '/manager.Manager/delKey',
}

// 获取活动请求链接信息
export function getActivityReqUrlInfo(url: string, commonParams: CommonReqParams) {
  const { activityId, customDomain, headers, ...otherParams } = commonParams || {};
  return {
    baseURL: customDomain ? `http://${customDomain}` : undefined,
    url: `${customDomain ? '' : `/${activityId}`}${url}`,
    headers,
    params: otherParams,
  };
}

export function checkSecretApi(params: CheckSecretParams) {
  return activityHttp.post<{}>({
    ...getActivityReqUrlInfo(Api.CheckSecret, params),
  });
}

export function getReportFnApi(params: CommonReqParams) {
  return activityHttp.post<GetReportFnResp>({
    ...getActivityReqUrlInfo(Api.GetReportFn, params),
  });
}

export function getReportListApi(params: CommonReqParams) {
  return activityHttp.post<GetReportListResp>({
    ...getActivityReqUrlInfo(Api.GetReportList, params),
  });
}

export function reportApi(params: ReportParams) {
  return activityHttp.post<{}>({
    ...getActivityReqUrlInfo(Api.Report, params),
  });
}

//////// envConf 相关接口 ////////
export function catEnvApi(params: CommonReqParams) {
  return activityHttp.post<CatEnvResp>(
    {
      ...getActivityReqUrlInfo(Api.CatEnv, params),
    },
    {
      apiUrl: '', // demo,服务名
      urlPrefix: '', // demo, 请求地址前缀
    },
  );
}

export function setEnvApi(params: SetEnvParams) {
  return activityHttp.post<{}>({
    ...getActivityReqUrlInfo(Api.SetEnv, params),
  });
}

export function delKeyApi(params: DelKeyParams) {
  return activityHttp.post<{}>({
    ...getActivityReqUrlInfo(Api.DelKey, params),
  });
}
