import { activityHttp } from '@/utils/http/axios';
import { CommonReqParams } from './model/reportModel';
import {
  GetLuckyDrawNamespaceListResp,
  GetLuckyDrawPoolParams,
  GetLuckyDrawPoolResp,
  MockLuckyDrawParams,
  MockLuckyDrawResp,
  ModifyLuckyDrawPoolParams,
  ModifyLuckyDrawPoolResp,
  SetNextLuckyDrawParams,
  SetNextLuckyDrawResp,
  GetRedemptionPoolParams,
  GetRedemptionPoolResp,
} from './model/luckyDrawModel';
import { getActivityReqUrlInfo } from './report';

enum Api {
  GetLuckyDrawNamespaceList = '/testluckydraw.TestLuckyDraw/getLuckyDrawNamespaceList',
  GetLuckyDrawPool = '/testluckydraw.TestLuckyDraw/getLuckyDrawPool',
  MockLuckyDraw = '/testluckydraw.TestLuckyDraw/mockLuckyDraw',
  ModifyLuckyDrawPool = '/testluckydraw.TestLuckyDraw/modifyLuckyDrawPool',
  SetNextLuckyDraw = '/testluckydraw.TestLuckyDraw/setNextLuckyDraw',
  GetRedemptionPool = '/testluckydraw.TestLuckyDraw/getRedemptionPool',
}

export function getLuckyDrawNamespaceListApi(params: CommonReqParams) {
  return activityHttp.post<GetLuckyDrawNamespaceListResp>({
    ...getActivityReqUrlInfo(Api.GetLuckyDrawNamespaceList, params),
  });
}

export function getLuckyDrawPoolApi(params: GetLuckyDrawPoolParams) {
  return activityHttp.post<GetLuckyDrawPoolResp>({
    ...getActivityReqUrlInfo(Api.GetLuckyDrawPool, params),
  });
}

export function mockLuckyDrawApi(params: MockLuckyDrawParams) {
  return activityHttp.post<MockLuckyDrawResp>({
    ...getActivityReqUrlInfo(Api.MockLuckyDraw, params),
  });
}

export function modifyLuckyDrawPoolApi(params: ModifyLuckyDrawPoolParams) {
  return activityHttp.post<ModifyLuckyDrawPoolResp>({
    ...getActivityReqUrlInfo(Api.ModifyLuckyDrawPool, params),
  });
}

export function setNextLuckyDrawApi(params: SetNextLuckyDrawParams) {
  return activityHttp.post<SetNextLuckyDrawResp>({
    ...getActivityReqUrlInfo(Api.SetNextLuckyDraw, params),
  });
}

export function getRedemptionPoolApi(params: GetRedemptionPoolParams) {
  return activityHttp.post<GetRedemptionPoolResp>({
    ...getActivityReqUrlInfo(Api.GetRedemptionPool, params),
  });
}
