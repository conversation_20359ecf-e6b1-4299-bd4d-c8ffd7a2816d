import { getUrlENV } from '@/utils/env';
import { createAxios } from '@/utils/http/axios';
import url from '@/utils/url';
import { GetListsParams, ObsTokenModel } from './model';
// import type { AxiosRequestConfig } from 'axios';
// import { RequestOptions } from '#/axios';
// import getMockData from './mockData';

const nodePath = 'prize-redemption-registry';
const stockMangePath = 'stock-manager-v2';
const env = getUrlENV();

const defHttp = createAxios({
  requestOptions: {
    // '//testing-tt-web-tc.ttyuyin.com/activity-testing'
    apiUrl: `${url.node_activity('//testing-tt-web-tc.ttyuyin.com/activity-testing')[env]}${/* env === 'dev' ? '' : */ `/${nodePath}`}`,
    // apiUrl: `${url.node_activity()['testing']}/${nodePath}`,
    urlPrefix: '/manager.Manager/',
  },
});

const defOperaionHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common('//testing-tt-web-tc.ttyuyin.com/common-testing')[env]}${/* env === 'dev' ? '' : */ `/${stockMangePath}`}`,
    urlPrefix: '/operation.Operation/',
  },
});

// const fetchApi = <T = any>(
//   config = {} as AxiosRequestConfig,
//   options?: RequestOptions,
//   fn?: Function,
// ) => {
//   const { mock //   if (mock) return getMockData(config.url, config.params, fn);

//   return defHttp.post<T>(config, options);
// };

enum Api {
  getObsToken = 'getObsToken',
  getLists = 'getLists',
  uploadUseImg = 'uploadUseImg',
  pushRewardMsg = 'pushRewardMsg',
  fillRemark = 'fillRemark',
}

// 获取上传token
export const getObsToken = () =>
  defHttp.post<ObsTokenModel>({ url: Api.getObsToken }, { urlPrefix: '/activity.Activity/' });

// 获取列表
export const getLists = (params: GetListsParams) =>
  defHttp.post<any>({ url: Api.getLists, params });

// 上传兑奖成功截图
export const uploadUseImg = (params: any) => defHttp.post<any>({ url: Api.uploadUseImg, params });

// 推送奖励到账消息通知
export const pushRewardMsg = (params: any) => defHttp.post<any>({ url: Api.pushRewardMsg, params });

// 填写备注
export const fillRemark = (params: { id: string; remark: string }) =>
  defHttp.post<any>({ url: Api.fillRemark, params });

// 通过CDKEY查询奖品信息
export const getCdkeyInfo = (params: { cdkey: string }) =>
  defOperaionHttp.post<any>({ url: 'getCdkeyInfo', params });
