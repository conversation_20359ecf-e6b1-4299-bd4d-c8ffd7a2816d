export interface AddActivityParams {
  activityId?: string;
  name?: string;
}
export interface GetActivityListParams {
  skip?: number;
  limit?: number;
  search?: string;
}
export interface GetPrizeParams {
  activityId?: string;
  skip?: number;
  limit?: number;
  search?: string;
  prizeId?: string;
}
export interface CronParams {
  cronMinute: string | number;
  cronHour: string | number;
  cronWeekday: string | number;
  num: string | number;
}
export interface AddPrizeParams {
  activityId?: string;
  prizeId?: string;
  name?: string;
  total?: number;
  inventoryId?: string;
  price?: string | number;
  crons: CronParams[];
}

export interface BatchPrizeParams {
  activityId?: string;
  list?: AddPrizeParams;
}

export interface RemovePrizeParams {
  activityId?: string;
  prizeId?: string;
  num?: number;
}

export interface ExchangeRecordParams {
  activityId?: string;
  endTime?: number;
  prizeId?: string;
  startTime?: number;
}
export interface InventoryParams {
  inventoryId?: string;
  name?: string;
}

export interface OutInventoryParams {
  inventoryId?: string;
  num?: number;
}

export interface ImportInventoryParams {
  inventoryId?: string;
  cdkeys?: string[];
}

export interface ActivityModel {
  activityId?: number;
  name?: string;
  createTime?: string;
  prizeNum?: number;
}

export interface PrizeModel {
  prizeId?: string;
  name?: string;
  inventoryId?: string;
  total?: number;
  stockLeft?: number;
  importNum?: number;
  price?: number;
  crons?: CronParams[];
}

export interface ExchangeRecordModel {
  uid?: number;
  ttid?: string;
  cdkey?: string;
  createdAt?: number;
  prizeId?: string;
}

export interface ObsTokenModel {
  token?: string;
}
