import { getUrlENV } from '@/utils/env';
import { createAxios } from '@/utils/http/axios';
import url from '@/utils/url';
import {
  AddActivityParams,
  GetActivityListParams,
  GetPrizeParams,
  AddPrizeParams,
  RemovePrizeParams,
  ExchangeRecordParams,
  ExchangeRecordModel,
  BatchPrizeParams,
  ImportInventoryParams,
  InventoryParams,
  OutInventoryParams,
} from './model';

const nodePath = 'stock-manager-v2';
const env = getUrlENV();

const defHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common()[env]}${env === 'dev' ? `/${nodePath}` : `/${nodePath}`}`,
    urlPrefix: '/stockManager.StockManager/',
  },
});

enum Api {
  getActList = 'getActList', // 获取活动列表
  submitAct = 'submitAct', // 新增活动
  getPrizeList = 'getPrizeList', // 获取礼物列表
  submitPrize = 'submitPrize', // 提交礼物信息
  batchSubmitPrize = 'batchSubmitPrize', //批量提交礼物信息
  removePrize = 'removePrize', // 删除礼物
  exchangeRecord = 'exchangeRecord', // 获取礼物发放列表
  manualImportStock = 'manualImportStock', // 手动导入N个CDKEY
  recycleStock = 'recycleStock', // 回收礼物CDKEY
  getRecordList = 'getRecordList', // 获取礼物发放列表
  getInventory = 'getInventory', // 获取外部礼包列表
  createInventory = 'createInventory', // 新建外部礼包
  removeInventory = 'removeInventory', // 删除外部礼包
  uploadInventory = 'uploadInventory', // 上传外部CDKEY
  recycleInventory = 'recycleInventory', // 导出外部CDKEY
}

export const getActList = (params: GetActivityListParams) =>
  defHttp.post<any>({ url: Api.getActList, params });

export const submitAct = async (params: AddActivityParams) => {
  const payload: any = {
    ...params,
  };
  defHttp.post<any>({ url: Api.submitAct, params: payload });
};
export const getPrizeListApi = (params: GetPrizeParams) =>
  defHttp.post<any>({ url: Api.getPrizeList, params });

export const submitPrizeApi = (params: AddPrizeParams) =>
  defHttp.post<any>({ url: Api.submitPrize, params });

export const batchSubmitPrizeApi = (params: BatchPrizeParams) =>
  defHttp.post<any>({ url: Api.batchSubmitPrize, params });

export const removePrizeApi = (params: RemovePrizeParams) =>
  defHttp.post<any>({ url: Api.removePrize, params });

export const recycleStockApi = (params: RemovePrizeParams) =>
  defHttp.post<any>({ url: Api.recycleStock, params });

export const manualImportStockApi = (params: RemovePrizeParams) =>
  defHttp.post<any>({ url: Api.manualImportStock, params });

export const getRecordListApi = (params: ExchangeRecordParams) =>
  defHttp.post<any>({ url: Api.getRecordList, params });

export const getInventoryApi = (params: GetPrizeParams) =>
  defHttp.post<any>({ url: Api.getInventory, params });

export const createInventoryApi = (params: InventoryParams) =>
  defHttp.post<any>({ url: Api.createInventory, params });

export const removeInventoryApi = (params: OutInventoryParams) =>
  defHttp.post<any>({ url: Api.removeInventory, params });

export const uploadInventoryApi = (params: ImportInventoryParams) =>
  defHttp.post<any>({ url: Api.uploadInventory, params });

export const recycleInventoryApi = (params: OutInventoryParams) =>
  defHttp.post<any>({ url: Api.recycleInventory, params });
