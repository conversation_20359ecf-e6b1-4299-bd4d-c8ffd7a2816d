// src/api/yunying/guild-store.ts
import { defHttp } from '@/utils/http/axios';

enum Api {
  // 奖品配置相关
  PRIZE_LIST = '/yunying/guild-store/prize/list',
  PRIZE_CREATE = '/yunying/guild-store/prize/create',
  PRIZE_UPDATE = '/yunying/guild-store/prize/update',
  PRIZE_DELETE = '/yunying/guild-store/prize/delete',

  // 兑换记录相关
  EXCHANGE_RECORD_LIST = '/yunying/guild-store/exchange/list',
  EXCHANGE_RECORD_EXPORT = '/yunying/guild-store/exchange/export',
  EXCHANGE_STATUS_UPDATE = '/yunying/guild-store/exchange/status/update',

  // 库存管理相关
  INVENTORY_LIST = '/yunying/guild-store/inventory/list',
  STOCK_UPDATE = '/yunying/guild-store/stock/update',
  STOCK_HISTORY_LIST = '/yunying/guild-store/stock/history/list',
}

// 奖品配置相关接口

/**
 * 获取奖品列表
 */
export function getPrizeList(params?: any) {
  return defHttp.get({
    url: Api.PRIZE_LIST,
    params,
  });
}

/**
 * 创建奖品
 */
export function createPrize(data: any) {
  return defHttp.post({
    url: Api.PRIZE_CREATE,
    data,
  });
}

/**
 * 更新奖品
 */
export function updatePrize(data: any) {
  return defHttp.put({
    url: Api.PRIZE_UPDATE,
    data,
  });
}

/**
 * 删除奖品
 */
export function deletePrize(ids: string[]) {
  return defHttp.delete({
    url: Api.PRIZE_DELETE,
    data: { ids },
  });
}

// 兑换记录相关接口

/**
 * 获取兑换记录列表
 */
export function getExchangeRecordList(params?: any) {
  return defHttp.get({
    url: Api.EXCHANGE_RECORD_LIST,
    params,
  });
}

/**
 * 导出兑换记录
 */
export function exportExchangeRecord(params?: any) {
  return defHttp.get({
    url: Api.EXCHANGE_RECORD_EXPORT,
    params,
    responseType: 'blob',
  });
}

/**
 * 更新兑换状态
 */
export function updateExchangeStatus(id: string, status: string) {
  return defHttp.put({
    url: Api.EXCHANGE_STATUS_UPDATE,
    data: { id, status },
  });
}

// 库存管理相关接口

/**
 * 获取库存列表
 */
export function getInventoryList(params?: any) {
  return defHttp.get({
    url: Api.INVENTORY_LIST,
    params,
  });
}

/**
 * 更新库存
 */
export function updateStock(data: any) {
  return defHttp.put({
    url: Api.STOCK_UPDATE,
    data,
  });
}

/**
 * 获取库存变更记录
 */
export function getStockHistoryList(params?: any) {
  return defHttp.get({
    url: Api.STOCK_HISTORY_LIST,
    params,
  });
}
