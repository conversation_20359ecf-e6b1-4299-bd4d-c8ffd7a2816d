// src/api/yunying/guild-store/mock.js
import dayjs from 'dayjs';

export const mock = {
  // 获取奖品列表
  getPrizeList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          list: [
            {
              id: '1',
              name: '100积分',
              description: '虚拟积分奖品',
              type: 'virtual',
              value: 100,
              cost: 50,
              stock: 1000,
              exchangeCondition: 1000,
              image: '/images/prize-points-100.png',
              status: 'active',
              sort: 1,
              createTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
              updateTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              id: '2',
              name: '苹果AirPods Pro',
              description: '苹果无线耳机',
              type: 'physical',
              value: 1999,
              cost: 2000,
              stock: 50,
              exchangeCondition: 100,
              image: '/images/prize-airpods.png',
              status: 'active',
              sort: 2,
              createTime: dayjs().subtract(5, 'day').format('YYYY-MM-DD HH:mm:ss'),
              updateTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              id: '3',
              name: 'iPhone 15 Pro',
              description: '苹果最新款手机',
              type: 'physical',
              value: 7999,
              cost: 8000,
              stock: 5,
              exchangeCondition: 20,
              image: '/images/prize-iphone15.png',
              status: 'active',
              sort: 3,
              createTime: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
              updateTime: dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss'),
            },
          ],
          total: 3,
          page: 1,
          pageSize: 10,
        });
      }, 300);
    });
  },

  // 创建奖品
  createPrize(data) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: `prize_${Date.now()}`,
        });
      }, 300);
    });
  },

  // 更新奖品
  updatePrize(data) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, 300);
    });
  },

  // 删除奖品
  deletePrize(ids) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, 300);
    });
  },

  // 推送奖品
  pushPrize(data) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, 300);
    });
  },

  // 获取兑换记录列表
  getExchangeRecordList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          list: [
            {
              id: 'ex001',
              ttid: '123456789',
              cdkey: '*********',
              describe: '王者荣耀-至尊宝皮肤',
              type: 'WZ',
              activityId: 'act_001',
              winningTime: dayjs().subtract(3, 'hour').format('YYYY-MM-DD HH:mm:ss'),
              isUse: true,
              useTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
              status: 'completed',
              gameId: '',
              gameName: '王者玩家001',
              gameImg: '/images/game-win-001.jpg',
              os: 'android',
              area: 'qq',
              hero: '至尊宝',
              gameHomeImg: '/images/game-home-001.jpg',
              gameWishImg: '/images/game-wish-001.jpg',
              useImg: '/images/use-success-001.jpg',
              remark: '已成功兑换',
            },
            {
              id: 'ex002',
              ttid: '987654321',
              cdkey: 'CJ2024002',
              describe: '和平精英-永久套装',
              type: 'CJ',
              activityId: 'act_001',
              winningTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
              isUse: true,
              useTime: dayjs().subtract(12, 'hour').format('YYYY-MM-DD HH:mm:ss'),
              status: 'completed',
              gameId: '12345678',
              gameName: '和平玩家002',
              gameImg: '/images/game-win-002.jpg',
              os: 'ios',
              area: 'weixin',
              gameHomeImg: '/images/game-home-002.jpg',
              useImg: '/images/use-success-002.jpg',
              remark: '已成功兑换',
            },
            {
              id: 'ex003',
              ttid: '555666777',
              cdkey: 'ENTITY2024003',
              describe: '苹果AirPods Pro',
              type: 'ENTITY',
              activityId: 'act_002',
              winningTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
              isUse: true,
              useTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
              status: 'processing',
              name: '张三',
              phone: '13800138000',
              address: '北京市朝阳区xxx街道xxx号',
              useImg: '/images/use-success-003.jpg',
              remark: '正在发货中',
            },
            {
              id: 'ex004',
              ttid: '111222333',
              cdkey: 'CASH2024004',
              describe: '现金红包-100元',
              type: 'CASH',
              activityId: 'act_003',
              winningTime: dayjs().subtract(6, 'hour').format('YYYY-MM-DD HH:mm:ss'),
              isUse: false,
              status: 'pending',
              name: '李四',
              alipay: '<EMAIL>',
              payCode: '/images/pay-code-004.jpg',
              remark: '待处理',
            },
            {
              id: 'ex005',
              ttid: '444555666',
              cdkey: 'WZ2024005',
              describe: '王者荣耀-传说皮肤',
              type: 'WZ',
              activityId: 'act_001',
              winningTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
              isUse: false,
              status: 'pending',
              gameName: '王者玩家005',
              gameImg: '/images/game-win-005.jpg',
              os: 'android',
              area: 'qq',
              hero: '传说皮肤',
              remark: '待兑换',
            },
          ],
          total: 5,
          page: 1,
          pageSize: 10,
        });
      }, 300);
    });
  },

  // 获取库存列表
  getInventoryList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          list: [
            {
              prizeId: '1',
              prizeName: '100积分',
              currentStock: 950,
              totalStock: 1000,
              usedStock: 50,
              reservedStock: 0,
              lastUpdateTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              prizeId: '2',
              prizeName: '苹果AirPods Pro',
              currentStock: 48,
              totalStock: 100,
              usedStock: 51,
              reservedStock: 1,
              lastUpdateTime: dayjs().subtract(12, 'hour').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              prizeId: '3',
              prizeName: 'iPhone 15 Pro',
              currentStock: 4,
              totalStock: 20,
              usedStock: 15,
              reservedStock: 1,
              lastUpdateTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
            },
          ],
          total: 3,
          page: 1,
          pageSize: 10,
        });
      }, 300);
    });
  },

  // 获取库存变更记录
  getStockHistoryList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          list: [
            {
              id: 'sh001',
              prizeId: '1',
              prizeName: '100积分',
              changeType: 'exchange',
              changeAmount: -1,
              beforeStock: 951,
              afterStock: 950,
              reason: '用户兑换',
              operatorId: 'system',
              operatorName: '系统',
              createTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              id: 'sh002',
              prizeId: '2',
              prizeName: '苹果AirPods Pro',
              changeType: 'reserve',
              changeAmount: -1,
              beforeStock: 49,
              afterStock: 48,
              reason: '用户兑换预留',
              operatorId: 'system',
              operatorName: '系统',
              createTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              id: 'sh003',
              prizeId: '3',
              prizeName: 'iPhone 15 Pro',
              changeType: 'reserve',
              changeAmount: -1,
              beforeStock: 5,
              afterStock: 4,
              reason: '用户兑换预留',
              operatorId: 'system',
              operatorName: '系统',
              createTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              id: 'sh004',
              prizeId: '2',
              prizeName: '苹果AirPods Pro',
              changeType: 'increase',
              changeAmount: 50,
              beforeStock: 50,
              afterStock: 100,
              reason: '管理员补充库存',
              operatorId: 'admin001',
              operatorName: '管理员',
              createTime: dayjs().subtract(5, 'day').format('YYYY-MM-DD HH:mm:ss'),
            },
          ],
          total: 4,
          page: 1,
          pageSize: 10,
        });
      }, 300);
    });
  },

  // 导出兑换记录
  exportExchangeRecord(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟返回 blob 数据
        const blob = new Blob(['兑换记录导出数据'], { type: 'application/vnd.ms-excel' });
        resolve(blob);
      }, 500);
    });
  },

  // 更新兑换状态
  updateExchangeStatus(id, status) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, 300);
    });
  },

  // 更新库存
  updateStock(data) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, 300);
    });
  },
};
