// src/api/yunying/guild-store/model/indexModal.ts

// 基础类型定义
export interface GuildInfo {
  guildId: number; // 公会ID
  displayId: number; // 展示ID，优先展示公会靓号
  name: string; // 公会名称
}

// 积分操作类型枚举
export enum PointOpType {
  POINT_OP_TYPE_UNKNOWN = 0,
  POINT_OP_TYPE_ACTIVITY_REWARD = 1, // 积分争霸赛活动奖励
  POINT_OP_TYPE_EXPIRE = 2, // 过期：时效到期
  POINT_OP_TYPE_REDEMPTION_CONSUME = 3, // 兑换消耗：后台扣除积分，下标读取后台备注文案
  POINT_OP_TYPE_MANUAL_GRANT = 4, // 运营发放：后台发放
}

// 奖品扣除信息
export interface PrizeDeduction {
  prizeId: number; // 奖品ID
  count: number; // 发放数量
  prizeName: string; // 奖品名称
}

// 商城商品相关类型定义
export interface MallGoods {
  prizeId?: number; // 奖品ID(修改时需传参)
  prizeName: string; // 奖品名称
  prizeImageUrl: string; // 奖品图片url
  displayWeight: number; // 展示权重
  displayStartTime: number; // 展示开始时间
  displayEndTime: number; // 展示结束时间
  redemptionPoints: number; // 兑换条件 - 单个奖品所需积分数
  guildRedemptionLimit: number; // 单个公会兑换限制-支持负数
  siteRedemptionLimit: number; // 全站兑换限制-支持负数
  remarks?: string; // 备注文案
}

// 兑换商品信息（活动页面用）
export interface RedeemGoods {
  prizeId: number; // ID
  prizeName: string; // 奖品名称
  prizeImageUrl: string; // 奖品图片
  redemptionPoints: number; // 兑换所需积分
  guildRedemptionLimit: number; // 单个公会兑换上限（-1 表示不限）
  guildRedeemedCount: number; // 当前公会已兑换数量
  siteRedemptionLimit: number; // 全站兑换上限（-1 表示不限）
  siteRedeemedCount: number; // 全站已兑换数量
  displayStartTime: number; // 展示开始时间
  displayEndTime: number; // 展示结束时间
  displayWeight: number; // 展示权重（用于前端排序）
}

// 向后兼容的奖品类型（保持原有接口）
export type Prize = MallGoods;

// 积分操作相关类型定义

// 发放积分请求
export interface GrantGuildPointsReq {
  ownerTtid: number; // 会长TTID
  points: number; // 发放积分（正整数）
  validDays: number; // 积分有效期，单位：自然天
  remark?: string; // 备注文案
}

// 扣除积分请求
export interface DeductGuildPointsReq {
  ownerTtid: number; // 会长TTID
  points: number; // 直接扣除积分（正整数）
  prizeInfo?: PrizeDeduction; // 发放奖品
  remark?: string; // 备注文案
}

// 积分操作记录项
export interface PointOperationItem {
  id: number; // 记录ID
  guildInfo: GuildInfo; // 公会信息
  ownerTtid: number; // 会长TTID
  ownerNickname: string; // 会长昵称
  pointsChange: number; // 积分变动，发放为正，扣除/过期为负
  opType: PointOpType; // 事件类型
  remark: string; // 备注
  operateTime: number; // 操作时间
}

// 公会积分库存项
export interface GuildPointInventoryItem {
  guildInfo: GuildInfo; // 公会信息（含 guildId、displayId、name）
  ownerTtid: number; // 会长TTID
  ownerNickname: string; // 会长昵称
  remainPoints: number; // 剩余积分
}

// 积分记录项（活动页面用）
export interface PointRecordItem {
  time: number; // 时间（秒）
  pointsChange: number; // 积分变动，发放为正，扣除/过期为负
  opType: PointOpType; // 事件类型
  remark?: string; // 备注
  userInfo?: any; // 贡献主播信息
  prizeInfo?: PrizeDeduction; // 发放奖品
}

// 兑换记录相关类型定义
export interface ExchangeRecord {
  id: string;
  ttid: string; // 用户TTID
  cdkey: string; // 兑奖码
  describe: string; // 兑奖明细
  type: 'WZ' | 'CJ' | 'ENTITY' | 'CASH'; // 兑奖种类：王者、和平精英、实物、现金
  activityId: string; // 所属活动id
  winningTime: string; // 中奖时间
  isUse: boolean; // 是否兑奖
  useTime?: string; // 兑奖时间
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'; // 处理状态
  gameId?: string; // 游戏ID(和平)
  gameName?: string; // 游戏昵称(王者、和平)
  gameImg?: string; // 中奖截图
  name?: string; // 姓名(现金、实物)
  phone?: string; // 电话(实物)
  address?: string; // 地址(实物)
  alipay?: string; // 支付宝账号(现金)
  wechat?: string; // 微信账号(现金)
  payCode?: string; // 收款码(现金)
  os?: 'ios' | 'android'; // 所在平台(王者、和平)
  area?: 'qq' | 'weixin'; // 所在区(王者、和平)
  hero?: string; // 所需英雄皮肤名字(王者)
  gameHomeImg?: string; // 游戏内个人主页截图(王者、和平)
  gameWishImg?: string; // 游戏内心愿单截图(王者)
  useImg?: string; // 兑奖成功截图
  remark?: string; // 备注
}

// 库存相关类型定义
export interface Inventory {
  prizeId: string;
  prizeName: string;
  currentStock: number; // 当前库存
  totalStock: number; // 总库存
  usedStock: number; // 已使用库存
  reservedStock: number; // 预留库存
  lastUpdateTime: string;
}

// 库存变更记录
export interface StockHistory {
  id: string;
  prizeId: string;
  prizeName: string;
  changeType: 'increase' | 'decrease' | 'exchange' | 'reserve' | 'release'; // 变更类型
  changeAmount: number; // 变更数量
  beforeStock: number; // 变更前库存
  afterStock: number; // 变更后库存
  reason: string; // 变更原因
  operatorId?: string;
  operatorName?: string;
  createTime: string;
}

// 请求和响应类型定义

// 商城商品列表请求
export interface ListMallGoodsReq {
  page?: number;
  size?: number;
}

// 商城商品列表响应
export interface ListMallGoodsRes {
  total: number;
  list: MallGoods[];
}

// 添加或更新商城商品请求
export type AddOrUpdateMallGoodsReq = MallGoods;

// 删除商城商品请求
export interface DeleteMallGoodsReq {
  prizeIdList: number[];
}

// 兑换商品列表请求
export interface ListRedeemGoodsReq {
  uid: number;
}

// 兑换商品列表响应
export interface ListRedeemGoodsRes {
  total: number;
  list: RedeemGoods[];
}

// 积分操作记录列表请求
export interface ListPointOperationsReq {
  page?: number;
  size?: number;
  startTime?: number; // 开始时间（秒）
  endTime?: number; // 结束时间（秒）
  guildId?: number; // 公会ID（任一条件可搜）
  ownerTtid?: number; // 会长TTID（任一条件可搜）
}

// 积分操作记录列表响应
export interface ListPointOperationsRes {
  total: number;
  list: PointOperationItem[];
}

// 导出积分操作记录响应
export interface ExportPointOperationsRes {
  url: string; // 导出下载地址
}

// 公会积分库存列表请求
export interface ListGuildPointInventoryReq {
  guildId?: number; // 公会ID（任一条件可搜）
  ownerTtid?: number; // 会长TTID（任一条件可搜）
}

// 公会积分库存列表响应
export interface ListGuildPointInventoryRes {
  total: number;
  list: GuildPointInventoryItem[];
}

// 导出公会积分库存响应
export interface ExportGuildPointInventoryRes {
  url: string; // 导出下载地址（excel）
}

// 我的公会积分响应
export interface GetMyGuildPointRes {
  point: number; // 可用总积分
  expireSoonPoint: number; // 临近过期积分
}

// 积分记录列表请求
export interface GetPointRecordsReq {
  page?: number;
  size?: number;
  uid: number; // 当前用户uid
}

// 积分记录列表响应
export interface GetPointRecordsRes {
  total: number;
  list: PointRecordItem[];
}

// 向后兼容的类型别名
export type PrizeListReq = ListMallGoodsReq;
export type PrizeListResp = ListMallGoodsRes;
export type PrizeCreateReq = AddOrUpdateMallGoodsReq;
export interface PrizeCreateResp {}
export type PrizeUpdateReq = AddOrUpdateMallGoodsReq;
export interface PrizeUpdateResp {}
export type PrizeDeleteReq = DeleteMallGoodsReq;
export interface PrizeDeleteResp {}

// 兑换记录列表
export interface ExchangeRecordListReq {
  page?: number;
  pageSize?: number;
  userId?: string;
  userName?: string;
  prizeId?: string;
  prizeName?: string;
  status?: ExchangeRecord['status'];
  startTime?: string;
  endTime?: string;
}

export interface ExchangeRecordListResp {
  list: ExchangeRecord[];
  total: number;
  page: number;
  pageSize: number;
}

// 导出兑换记录
export type ExchangeRecordExportReq = Omit<ExchangeRecordListReq, 'page' | 'pageSize'>;

// 更新兑换状态
export interface ExchangeStatusUpdateReq {
  id: string;
  status: ExchangeRecord['status'];
  remark?: string;
}
export interface ExchangeStatusUpdateResp {}

// 库存列表
export interface InventoryListReq {
  page?: number;
  pageSize?: number;
  prizeId?: string;
  prizeName?: string;
}

export interface InventoryListResp {
  list: Inventory[];
  total: number;
  page: number;
  pageSize: number;
}

// 更新库存
export interface StockUpdateReq {
  prizeId: string;
  changeType: StockHistory['changeType'];
  changeAmount: number;
  reason: string;
}
export interface StockUpdateResp {}

// 库存变更记录列表
export interface StockHistoryListReq {
  page?: number;
  pageSize?: number;
  prizeId?: string;
  prizeName?: string;
  changeType?: StockHistory['changeType'];
  startTime?: string;
  endTime?: string;
}

export interface StockHistoryListResp {
  list: StockHistory[];
  total: number;
  page: number;
  pageSize: number;
}
