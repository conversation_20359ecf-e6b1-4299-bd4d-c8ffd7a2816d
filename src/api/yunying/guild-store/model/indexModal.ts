// src/api/yunying/guild-store/model/indexModal.ts

// 奖品相关类型定义
export interface Prize {
  id?: string;
  name: string;
  description?: string;
  type: 'virtual' | 'physical'; // 虚拟奖品或实物奖品
  value: number; // 奖品价值
  cost: number; // 兑换消耗积分
  stock: number; // 库存数量
  totalStock: number; // 总库存
  image?: string; // 奖品图片
  status: 'active' | 'inactive'; // 状态
  sort: number; // 排序
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  createTime?: string;
  updateTime?: string;
}

// 兑换记录相关类型定义
export interface ExchangeRecord {
  id: string;
  ttid: string; // 用户TTID
  cdkey: string; // 兑奖码
  describe: string; // 兑奖明细
  type: 'WZ' | 'CJ' | 'ENTITY' | 'CASH'; // 兑奖种类：王者、和平精英、实物、现金
  activityId: string; // 所属活动id
  winningTime: string; // 中奖时间
  isUse: boolean; // 是否兑奖
  useTime?: string; // 兑奖时间
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'; // 处理状态
  gameId?: string; // 游戏ID(和平)
  gameName?: string; // 游戏昵称(王者、和平)
  gameImg?: string; // 中奖截图
  name?: string; // 姓名(现金、实物)
  phone?: string; // 电话(实物)
  address?: string; // 地址(实物)
  alipay?: string; // 支付宝账号(现金)
  wechat?: string; // 微信账号(现金)
  payCode?: string; // 收款码(现金)
  os?: 'ios' | 'android'; // 所在平台(王者、和平)
  area?: 'qq' | 'weixin'; // 所在区(王者、和平)
  hero?: string; // 所需英雄皮肤名字(王者)
  gameHomeImg?: string; // 游戏内个人主页截图(王者、和平)
  gameWishImg?: string; // 游戏内心愿单截图(王者)
  useImg?: string; // 兑奖成功截图
  remark?: string; // 备注
}

// 库存相关类型定义
export interface Inventory {
  prizeId: string;
  prizeName: string;
  currentStock: number; // 当前库存
  totalStock: number; // 总库存
  usedStock: number; // 已使用库存
  reservedStock: number; // 预留库存
  lastUpdateTime: string;
}

// 库存变更记录
export interface StockHistory {
  id: string;
  prizeId: string;
  prizeName: string;
  changeType: 'increase' | 'decrease' | 'exchange' | 'reserve' | 'release'; // 变更类型
  changeAmount: number; // 变更数量
  beforeStock: number; // 变更前库存
  afterStock: number; // 变更后库存
  reason: string; // 变更原因
  operatorId?: string;
  operatorName?: string;
  createTime: string;
}

// 请求和响应类型定义

// 奖品列表
export interface PrizeListReq {
  page?: number;
  pageSize?: number;
  name?: string;
  type?: 'virtual' | 'physical';
  status?: 'active' | 'inactive';
}

export interface PrizeListResp {
  list: Prize[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建奖品
export type PrizeCreateReq = Omit<Prize, 'id' | 'createTime' | 'updateTime'>;
export interface PrizeCreateResp {
  id: string;
}

// 更新奖品
export type PrizeUpdateReq = Partial<Prize> & { id: string };
export interface PrizeUpdateResp {}

// 删除奖品
export interface PrizeDeleteReq {
  ids: string[];
}
export interface PrizeDeleteResp {}

// 兑换记录列表
export interface ExchangeRecordListReq {
  page?: number;
  pageSize?: number;
  userId?: string;
  userName?: string;
  prizeId?: string;
  prizeName?: string;
  status?: ExchangeRecord['status'];
  startTime?: string;
  endTime?: string;
}

export interface ExchangeRecordListResp {
  list: ExchangeRecord[];
  total: number;
  page: number;
  pageSize: number;
}

// 导出兑换记录
export type ExchangeRecordExportReq = Omit<ExchangeRecordListReq, 'page' | 'pageSize'>;

// 更新兑换状态
export interface ExchangeStatusUpdateReq {
  id: string;
  status: ExchangeRecord['status'];
  remark?: string;
}
export interface ExchangeStatusUpdateResp {}

// 库存列表
export interface InventoryListReq {
  page?: number;
  pageSize?: number;
  prizeId?: string;
  prizeName?: string;
}

export interface InventoryListResp {
  list: Inventory[];
  total: number;
  page: number;
  pageSize: number;
}

// 更新库存
export interface StockUpdateReq {
  prizeId: string;
  changeType: StockHistory['changeType'];
  changeAmount: number;
  reason: string;
}
export interface StockUpdateResp {}

// 库存变更记录列表
export interface StockHistoryListReq {
  page?: number;
  pageSize?: number;
  prizeId?: string;
  prizeName?: string;
  changeType?: StockHistory['changeType'];
  startTime?: string;
  endTime?: string;
}

export interface StockHistoryListResp {
  list: StockHistory[];
  total: number;
  page: number;
  pageSize: number;
}
