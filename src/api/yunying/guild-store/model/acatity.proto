
syntax = "proto3";

import "common.proto";

package activity;

service Activity {
  // 初始
  rpc init(userReq) returns(initRes);
  // 小时榜
  rpc getHourRank(getHourRankReq) returns(getHourRankRes);
  // 公会榜
  rpc getGuildRank(getGuildRankReq) returns(getGuildRankRes);
  // 公会-主播榜
  rpc getGuildAnchorRank(getGuildAnchorRankReq) returns(getHourRankRes);
  // 活动页面-我的公会积分
  rpc getMyGuildPoint(userReq) returns(getMyGuildPointRes);
  // 活动页面-积分记录列表
  rpc getPointRecords(getPointRecordsReq) returns(getPointRecordsRes);
  // 增加商城商品
  rpc addMallGoods(addOrUpdateMallGoodsReq) returns(Empty);
  // 修改商城商品
  rpc updateMallGoods(addOrUpdateMallGoodsReq) returns(Empty);
  // 删除商城商品
  rpc deleteMallGoods(deleteMallGoodsReq) returns(Empty);
  // 推送商城商品
  rpc pushMallGoods(Empty) returns(Empty);
  // 商城后台-商品分页列表
  rpc listMallGoods(listMallGoodsReq) returns(listMallGoodsRes);
  // 活动页面-积分兑换商品列表
  rpc listRedeemGoods(listRedeemGoodsReq) returns(listRedeemGoodsRes);
  // 发放积分（仅正整数），支持设置有效期天数与备注
  rpc grantGuildPoints(grantGuildPointsReq) returns(Empty);
  // 扣除积分（仅正整数），支持按积分或按奖品与数量扣除
  rpc deductGuildPoints(deductGuildPointsReq) returns(Empty);
  // 查询积分变动记录（支持时间、公会、会长TTID筛选）
  rpc listPointOperations(listPointOperationsReq) returns(listPointOperationsRes);
  // 导出积分变动记录（与查询条件一致），返回下载地址
  rpc exportPointOperations(listPointOperationsReq) returns(exportPointOperationsRes);
  // 公会剩余积分排行榜（无时间筛选，按剩余积分从高到低）
  rpc listGuildPointInventory(listGuildPointInventoryReq) returns(listGuildPointInventoryRes);
  // 导出公会剩余积分排行榜（与查询条件一致），返回下载地址
  rpc exportGuildPointInventory(listGuildPointInventoryReq) returns(exportGuildPointInventoryRes);
  
}

message Empty {

}

message userReq {
  uint32 uid = 1; // 当前用户uid
  string token = 2; // 当前用户token
}

message initReq {
  uint32 uid = 1; // 当前用户uid
  string token = 2; // 当前用户token
  uint32 anchorUid = 3; // 跟随主播uid
}

message initRes {
  uint32 serverTime = 1; // 服务器时间
  uint32 startTime = 2; // 活动开始时间
  uint32 endTime = 3;  // 活动结束时间
  common.UserInfo userInfo = 4; // 用户信息
  uint32 isAnchor = 5; // 是否是主播 0-否
  uint32 hourRankRaceId = 6; // 小时榜赛道ID 1-领航赛道 2-进阶赛道 3-新星赛道
  optional GuildInfo guildInfo = 7; // 公会信息
}

message getHourRankReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
  optional string date = 4; // 榜单日期 YYYYMMDD 20230501 不传默认为总榜
  optional string hour = 5; // 小时
  optional uint32 raceId = 6; // 赛道ID 1-领航赛道 2-进阶赛道 3-新星赛道
}

message myRankInfo {
  common.UserInfo userInfo = 1; // 用户信息
  string rank = 2; // 榜单排名
  uint32 value  = 3; // 榜单值
  string prevDescribe = 4; // 距上一名描述
  string nextDescribe = 5; // 超下一名描述
  uint32 point = 6; // 积分
  bool isReach = 7; // 是否达到门槛
}

message getHourRankRes {
   message Item {
      string rank = 1; // 榜单排名
      uint32 value = 2; // 榜单值
      common.UserInfo userInfo = 3; // 用户信息
      optional common.ChannelInfo channelInfo = 4; // 房间信息
      uint32 point = 6; // 积分
      bool isReach = 7; // 是否达到门槛
  }
  uint32 total = 1; // 总数
  repeated Item list = 2; // 列表信息
  optional myRankInfo self = 3; // 底部我的排名信息
}

message GuildInfo {
  uint32 guildId      = 1;// 公会ID
  uint32 displayId    = 2; // 展示ID，优先展示公会靓号
  string name         = 3; // 公会名称
}

message getGuildRankReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
}

message MeOfGuildRank {
  GuildInfo guildInfo = 1; // 公会信息
  uint32 value        = 4; // 值
  string rank         = 5; // 排名
  string prevDescribe = 6; // 距上一名描述
  string nextDescribe = 7; // 超下一名描述
  uint32 point = 8; // 积分
}

// 公会信息
message GuildOfRanking {
  GuildInfo guildInfo = 1; // 公会信息
  uint32 value        = 2; // 值
  string rank         = 3; // 排名
  uint32 point = 4; // 积分
}

message getGuildRankRes {
  uint32 total = 1;
  optional MeOfGuildRank self = 2;
  repeated GuildOfRanking list = 3;
}

message getGuildAnchorRankReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
  uint32 guildId       = 4; // 公会ID
}

// ===== 商城配置 =====
message addOrUpdateMallGoodsReq {
  string prizeName = 1; // 奖品名称
  string prizeImageUrl = 2; // 奖品图片url
  uint32 displayWeight = 3; // 展示权重
  uint32 displayStartTime = 4; // 展示开始时间
  uint32 displayEndTime = 5; // 展示结束时间
  uint32 redemptionPoints = 6; // 兑换条件 - 单个奖品所需积分数
  int32 guildRedemptionLimit = 7; // 单个公会兑换限制-支持负数
  int32 siteRedemptionLimit = 8; // 全站兑换限制-支持负数
  optional string remarks = 9; // 备注文案
  optional uint32 prizeId = 10; // 奖品ID(修改时需传参)
}

message deleteMallGoodsReq {
  repeated uint32 prizeIdList = 1; // 奖品ID数组
}

// 商城后台-商品分页列表
message listMallGoodsReq {
  uint32 page = 1 [default = 1];
  uint32 size = 2 [default = 10];
}

message listMallGoodsRes {
  message Item {
    uint32 prizeId = 1;           // ID（自增）
    string prizeName = 2;         // 奖品名称
    string prizeImageUrl = 3;     // 奖品图片
    uint32 displayWeight = 4;     // 展示权重
    uint32 displayStartTime = 5;  // 展示开始时间
    uint32 displayEndTime = 6;    // 展示结束时间
    uint32 redemptionPoints = 7;   // 兑换条件（积分）
    int32 guildRedemptionLimit = 8; // 单个公会兑换限制-支持负数
    int32 siteRedemptionLimit = 9;  // 全站兑换限制-支持负数
  }
  uint32 total = 1;          // 总数
  repeated Item list = 2;    // 列表
}

// ===== 积分操作 =====

enum PointOpType {
  POINT_OP_TYPE_UNKNOWN = 0;
  POINT_OP_TYPE_ACTIVITY_REWARD = 1; // 积分争霸赛活动奖励
  POINT_OP_TYPE_EXPIRE = 2;          // 过期：时效到期
  POINT_OP_TYPE_REDEMPTION_CONSUME = 3; // 兑换消耗：后台扣除积分，下标读取后台备注文案
  POINT_OP_TYPE_MANUAL_GRANT = 4;    // 运营发放：后台发放
}

// 发放积分弹窗：会长TTID、发放积分(仅支持正整数)、积分有效期(天)、备注文案
message grantGuildPointsReq {
  uint32 ownerTtid = 1; // 会长TTID
  uint32 points = 2; // 发放积分（正整数）
  uint32 validDays = 3; // 积分有效期，单位：自然天
  optional string remark = 4; // 备注文案
}

message PrizeDeduction {
  uint32 prizeId = 1; // 奖品ID
  uint32 count = 2; // 发放数量
  string prizeName = 3; // 奖品名称
}

// 扣除积分弹窗：会长TTID、扣除积分(仅支持正整数)；也可选择奖品与数量按配置扣除
message deductGuildPointsReq {
  uint32 ownerTtid = 1; // 会长TTID
  uint32 points = 2; // 直接扣除积分（正整数）
  optional PrizeDeduction prizeInfo = 3; // 发放奖品
  optional string remark = 4; // 备注文案
}

// 列表页筛选：时间范围、公会ID、会长TTID；支持分页
message listPointOperationsReq {
  uint32 page = 1 [default = 1];
  uint32 size = 2 [default = 10];
  optional uint32 startTime = 3; // 开始时间（秒）
  optional uint32 endTime = 4; // 结束时间（秒）
  optional uint32 guildId = 5; // 公会ID（任一条件可搜）
  optional uint32 ownerTtid = 6; // 会长TTID（任一条件可搜）
}

message PointOperationItem {
  uint32 id = 1; // 记录ID
  GuildInfo guildInfo = 2; // 公会信息
  uint32 ownerTtid = 3; // 会长TTID
  string ownerNickname = 4; // 会长昵称
  int32 pointsChange = 7; // 积分变动，发放为正，扣除/过期为负
  PointOpType opType = 8; // 事件类型
  string remark = 9; // 备注
  uint32 operateTime = 10; // 操作时间
}

message listPointOperationsRes {
  uint32 total = 1; // 总数
  repeated PointOperationItem list = 2; // 列表
}

message exportPointOperationsRes {
  string url = 1; // 导出下载地址
}

// ===== 公会剩余积分排行榜 =====
message listGuildPointInventoryReq {
  optional uint32 guildId = 1; // 公会ID（任一条件可搜）
  optional uint32 ownerTtid = 2; // 会长TTID（任一条件可搜）
}

message GuildPointInventoryItem {
  GuildInfo guildInfo = 1; // 公会信息（含 guildId、displayId、name）
  uint32 ownerTtid = 2;    // 会长TTID
  string ownerNickname = 3;// 会长昵称
  uint32 remainPoints = 4; // 剩余积分
}

message listGuildPointInventoryRes {
  uint32 total = 1; // 总数
  repeated GuildPointInventoryItem list = 2; // 列表，按 remainPoints 从高到低
}

message exportGuildPointInventoryRes {
  string url = 1; // 导出下载地址（excel）
}

// ===== 活动页面 =====
message getMyGuildPointRes {
  uint32 point = 1; // 可用总积分
  uint32 expireSoonPoint = 2; // 临近过期积分
}

message getPointRecordsReq {
  uint32 page = 1 [default = 1];
  uint32 size = 2 [default = 10];
  uint32 uid = 3; // 当前用户uid
}

message PointRecordItem {
  uint32 time = 1; // 时间（秒）
  int32 pointsChange = 2; // 积分变动，发放为正，扣除/过期为负
  PointOpType opType = 3; // 事件类型
  optional string remark = 4; // 备注
  optional common.UserInfo userInfo = 5; // 贡献主播信息
  optional PrizeDeduction prizeInfo = 6; // 发放奖品
}

message getPointRecordsRes {
  uint32 total = 1; // 总数
  repeated PointRecordItem list = 2; // 列表
}

// 活动页面-积分兑换商品列表
message listRedeemGoodsReq {
  uint32 uid = 1; // 当前用户uid
}

message listRedeemGoodsRes {
  message Item {
    uint32 prizeId = 1;            // ID
    string prizeName = 2;          // 奖品名称
    string prizeImageUrl = 3;      // 奖品图片
    uint32 redemptionPoints = 4;   // 兑换所需积分
    int32 guildRedemptionLimit = 5; // 单个公会兑换上限（-1 表示不限）
    uint32 guildRedeemedCount = 6;  // 当前公会已兑换数量
    int32 siteRedemptionLimit = 7;  // 全站兑换上限（-1 表示不限）
    uint32 siteRedeemedCount = 8;   // 全站已兑换数量
    uint32 displayStartTime = 9;    // 展示开始时间
    uint32 displayEndTime = 10;     // 展示结束时间
    uint32 displayWeight = 11;      // 展示权重（用于前端排序）
  }
  uint32 total = 1;         // 总数
  repeated Item list = 2;   // 列表
}
