// src/api/yunying/guild-store/index.ts
import { activityHttp } from '@/utils/http/axios';
import { urlParams } from '@/utils/url';
import {
  PrizeListReq,
  PrizeListResp,
  PrizeCreateReq,
  PrizeCreateResp,
  PrizeUpdateReq,
  PrizeUpdateResp,
  PrizeDeleteReq,
  PrizeDeleteResp,
  ExchangeRecordListReq,
  ExchangeRecordListResp,
  ExchangeRecordExportReq,
  ExchangeStatusUpdateReq,
  ExchangeStatusUpdateResp,
  InventoryListReq,
  InventoryListResp,
  StockUpdateReq,
  StockUpdateResp,
  StockHistoryListReq,
  StockHistoryListResp,
} from './model/indexModal';
import { mock } from './mock';
// 获取活动请求链接
export function getActivityReqUrl(url) {
  return {
    url: `/lucky-party-igbt${url}`,
  };
}
enum Api {
  // 奖品配置相关
  PRIZE_LIST = '/admin.Admin/getPrizeList',
  PRIZE_CREATE = '/admin.Admin/createPrize',
  PRIZE_UPDATE = '/admin.Admin/updatePrize',
  PRIZE_DELETE = '/admin.Admin/deletePrize',
  PRIZE_PUSH = '/admin.Admin/pushPrize',

  // 兑换记录相关
  EXCHANGE_RECORD_LIST = '/admin.Admin/getExchangeRecordList',
  EXCHANGE_RECORD_EXPORT = '/admin.Admin/exportExchangeRecord',
  EXCHANGE_STATUS_UPDATE = '/admin.Admin/updateExchangeStatus',

  // 库存管理相关
  INVENTORY_LIST = '/admin.Admin/getInventoryList',
  STOCK_UPDATE = '/admin.Admin/updateStock',
  STOCK_HISTORY_LIST = '/admin.Admin/getStockHistoryList',
}

// 奖品配置相关接口

/**
 * 获取奖品列表
 */
export function getPrizeList(params?: PrizeListReq) {
  if (urlParams.myMock) {
    return mock.getPrizeList() as Promise<PrizeListResp>;
  }
  return activityHttp.post<PrizeListResp>({
    ...getActivityReqUrl(Api.PRIZE_LIST),
    params,
  });
}

/**
 * 创建奖品
 */
export function createPrize(data: PrizeCreateReq | any) {
  if (urlParams.myMock) {
    return mock.createPrize(data) as Promise<PrizeCreateResp>;
  }
  return activityHttp.post<PrizeCreateResp>({
    ...getActivityReqUrl(Api.PRIZE_CREATE),
    data,
  });
}

/**
 * 更新奖品
 */
export function updatePrize(data: PrizeUpdateReq) {
  if (urlParams.myMock) {
    return mock.updatePrize(data) as Promise<PrizeUpdateResp>;
  }
  return activityHttp.put<PrizeUpdateResp>({
    ...getActivityReqUrl(Api.PRIZE_UPDATE),
    data,
  });
}

/**
 * 删除奖品
 */
export function deletePrize(ids: string[]) {
  if (urlParams.myMock) {
    return mock.deletePrize(ids) as Promise<PrizeDeleteResp>;
  }
  return activityHttp.delete<PrizeDeleteResp>({
    ...getActivityReqUrl(Api.PRIZE_DELETE),
    data: { ids },
  });
}

/**
 * 推送奖品
 */
export function pushPrize(data: any) {
  if (urlParams.myMock) {
    return mock.pushPrize(data) as Promise<any>;
  }
  return activityHttp.post({
    ...getActivityReqUrl(Api.PRIZE_PUSH),
    data,
  });
}

// 兑换记录相关接口

/**
 * 获取兑换记录列表
 */
export function getExchangeRecordList(params?: ExchangeRecordListReq) {
  if (urlParams.myMock) {
    return mock.getExchangeRecordList() as Promise<ExchangeRecordListResp>;
  }
  return activityHttp.post<ExchangeRecordListResp>({
    ...getActivityReqUrl(Api.EXCHANGE_RECORD_LIST),
    params,
  });
}

/**
 * 导出兑换记录
 */
export function exportExchangeRecord(params?: ExchangeRecordExportReq) {
  if (urlParams.myMock) {
    return mock.exportExchangeRecord(params) as Promise<any>;
  }
  return activityHttp.post({
    ...getActivityReqUrl(Api.EXCHANGE_RECORD_EXPORT),
    params,
    responseType: 'blob',
  });
}

/**
 * 更新兑换状态
 */
export function updateExchangeStatus(id: string, status: string) {
  if (urlParams.myMock) {
    return mock.updateExchangeStatus(id, status) as Promise<ExchangeStatusUpdateResp>;
  }
  return activityHttp.put<ExchangeStatusUpdateResp>({
    ...getActivityReqUrl(Api.EXCHANGE_STATUS_UPDATE),
    data: { id, status },
  });
}

// 库存管理相关接口

/**
 * 获取库存列表
 */
export function getInventoryList(params?: InventoryListReq) {
  if (urlParams.myMock) {
    return mock.getInventoryList() as Promise<InventoryListResp>;
  }
  return activityHttp.post<InventoryListResp>({
    ...getActivityReqUrl(Api.INVENTORY_LIST),
    params,
  });
}

/**
 * 更新库存
 */
export function updateStock(data: StockUpdateReq) {
  if (urlParams.myMock) {
    return mock.updateStock(data) as Promise<StockUpdateResp>;
  }
  return activityHttp.put<StockUpdateResp>({
    ...getActivityReqUrl(Api.STOCK_UPDATE),
    data,
  });
}

/**
 * 获取库存变更记录
 */
export function getStockHistoryList(params?: StockHistoryListReq) {
  if (urlParams.myMock) {
    return mock.getStockHistoryList() as Promise<StockHistoryListResp>;
  }
  return activityHttp.post<StockHistoryListResp>({
    ...getActivityReqUrl(Api.STOCK_HISTORY_LIST),
    params,
  });
}
