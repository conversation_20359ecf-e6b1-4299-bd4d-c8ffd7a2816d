/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
// src/api/yunying/guild-store/index.ts
import { activityHttp } from '@/utils/http/axios';
import { urlParams } from '@/utils/url';
import {
  // 商城商品相关
  ListMallGoodsReq,
  ListMallGoodsRes,
  AddOrUpdateMallGoodsReq,
  DeleteMallGoodsReq,
  ListRedeemGoodsReq,
  ListRedeemGoodsRes,

  // 积分管理相关
  GrantGuildPointsReq,
  DeductGuildPointsReq,
  ListPointOperationsReq,
  ListPointOperationsRes,
  ExportPointOperationsRes,
  ListGuildPointInventoryReq,
  ListGuildPointInventoryRes,
  ExportGuildPointInventoryRes,
  GetMyGuildPointRes,
  GetPointRecordsReq,
  GetPointRecordsRes,

  // 向后兼容的类型
  PrizeListReq,
  PrizeListResp,
  PrizeCreateReq,
  PrizeCreateResp,
  PrizeUpdateReq,
  PrizeUpdateResp,
  PrizeDeleteReq,
  PrizeDeleteResp,
  ExchangeRecordListReq,
  ExchangeRecordListResp,
  ExchangeRecordExportReq,
  ExchangeStatusUpdateReq,
  ExchangeStatusUpdateResp,
  InventoryListReq,
  InventoryListResp,
  StockUpdateReq,
  StockUpdateResp,
  StockHistoryListReq,
  StockHistoryListResp,
} from './model/indexModal';
import { mock } from './mock';
import { useUserStore } from '@/store/modules/user';
import { computed } from 'vue';

const userStore = useUserStore();

const getUserInfo = computed(() => userStore.getUserInfo.username);
// 获取活动请求链接
export function getActivityReqUrl(url) {
  return {
    url: `/live-guild-points-mall${url}`,
  };
}
enum Api {
  // 商城商品管理
  ADD_MALL_GOODS = '/activity.Activity/addMallGoods',
  UPDATE_MALL_GOODS = '/activity.Activity/updateMallGoods',
  DELETE_MALL_GOODS = '/activity.Activity/deleteMallGoods',
  PUSH_MALL_GOODS = '/activity.Activity/pushMallGoods',
  LIST_MALL_GOODS = '/activity.Activity/listMallGoods',
  LIST_REDEEM_GOODS = '/activity.Activity/listRedeemGoods',

  // 积分管理
  GRANT_GUILD_POINTS = '/activity.Activity/grantGuildPoints',
  DEDUCT_GUILD_POINTS = '/activity.Activity/deductGuildPoints',
  LIST_POINT_OPERATIONS = '/activity.Activity/listPointOperations',
  EXPORT_POINT_OPERATIONS = '/activity.Activity/exportPointOperations',
  LIST_GUILD_POINT_INVENTORY = '/activity.Activity/listGuildPointInventory',
  EXPORT_GUILD_POINT_INVENTORY = '/activity.Activity/exportGuildPointInventory',

  // 活动页面
  GET_MY_GUILD_POINT = '/activity.Activity/getMyGuildPoint',
  GET_POINT_RECORDS = '/activity.Activity/getPointRecords',

  // 向后兼容的别名
  PRIZE_LIST = '/activity.Activity/listMallGoods',
  PRIZE_CREATE = '/activity.Activity/addMallGoods',
  PRIZE_UPDATE = '/activity.Activity/updateMallGoods',
  PRIZE_DELETE = '/activity.Activity/deleteMallGoods',
  PRIZE_PUSH = '/activity.Activity/pushMallGoods',

  // 兑换记录相关（保持兼容）
  EXCHANGE_RECORD_LIST = '/activity.Activity/getExchangeRecordList',
  EXCHANGE_RECORD_EXPORT = '/activity.Activity/exportExchangeRecord',
  EXCHANGE_STATUS_UPDATE = '/activity.Activity/updateExchangeStatus',

  // 库存管理相关（保持兼容）
  INVENTORY_LIST = '/activity.Activity/getInventoryList',
  STOCK_UPDATE = '/activity.Activity/updateStock',
  STOCK_HISTORY_LIST = '/activity.Activity/getStockHistoryList',
}

// 奖品配置相关接口

/**
 * 获取奖品列表
 */
export function getPrizeList(params?: PrizeListReq) {
  if (urlParams.myMock) {
    return mock.getPrizeList() as Promise<PrizeListResp>;
  }
  return activityHttp.post<PrizeListResp>({
    ...getActivityReqUrl(Api.PRIZE_LIST),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 创建奖品
 */
export function createPrize(data: PrizeCreateReq | any) {
  if (urlParams.myMock) {
    return mock.createPrize(data) as Promise<PrizeCreateResp>;
  }
  return activityHttp.post<PrizeCreateResp>({
    ...getActivityReqUrl(Api.PRIZE_CREATE),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 更新奖品
 */
export function updatePrize(data: PrizeUpdateReq) {
  if (urlParams.myMock) {
    return mock.updatePrize(data) as Promise<PrizeUpdateResp>;
  }
  return activityHttp.post<PrizeUpdateResp>({
    ...getActivityReqUrl(Api.PRIZE_UPDATE),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 删除奖品
 */
export function deletePrize(ids: string[]) {
  if (urlParams.myMock) {
    return mock.deletePrize(ids) as Promise<PrizeDeleteResp>;
  }
  return activityHttp.post<PrizeDeleteResp>({
    ...getActivityReqUrl(Api.PRIZE_DELETE),
    data: {
      ids,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 推送奖品
 */
export function pushPrize(data: any) {
  if (urlParams.myMock) {
    return mock.pushPrize(data) as Promise<any>;
  }
  return activityHttp.post({
    ...getActivityReqUrl(Api.PRIZE_PUSH),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

// 兑换记录相关接口

/**
 * 获取兑换记录列表
 */
export function getExchangeRecordList(params?: ExchangeRecordListReq) {
  if (urlParams.myMock) {
    return mock.getExchangeRecordList() as Promise<ExchangeRecordListResp>;
  }
  return activityHttp.post<ExchangeRecordListResp>({
    ...getActivityReqUrl(Api.EXCHANGE_RECORD_LIST),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 导出兑换记录
 */
export function exportExchangeRecord(params?: ExchangeRecordExportReq) {
  if (urlParams.myMock) {
    return mock.exportExchangeRecord(params) as Promise<any>;
  }
  return activityHttp.post({
    ...getActivityReqUrl(Api.EXCHANGE_RECORD_EXPORT),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
    responseType: 'blob',
  });
}

/**
 * 更新兑换状态
 */
export function updateExchangeStatus(id: string, status: string) {
  if (urlParams.myMock) {
    return mock.updateExchangeStatus(id, status) as Promise<ExchangeStatusUpdateResp>;
  }
  return activityHttp.post<ExchangeStatusUpdateResp>({
    ...getActivityReqUrl(Api.EXCHANGE_STATUS_UPDATE),
    data: {
      id,
      status,
      operatorName: getUserInfo.value,
    },
  });
}

// 库存管理相关接口

/**
 * 获取库存列表
 */
export function getInventoryList(params?: InventoryListReq) {
  if (urlParams.myMock) {
    return mock.getInventoryList() as Promise<InventoryListResp>;
  }
  return activityHttp.post<InventoryListResp>({
    ...getActivityReqUrl(Api.INVENTORY_LIST),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 更新库存
 */
export function updateStock(data: StockUpdateReq) {
  if (urlParams.myMock) {
    return mock.updateStock(data) as Promise<StockUpdateResp>;
  }
  return activityHttp.post<StockUpdateResp>({
    ...getActivityReqUrl(Api.STOCK_UPDATE),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 获取库存变更记录
 */
export function getStockHistoryList(params?: StockHistoryListReq) {
  if (urlParams.myMock) {
    return mock.getStockHistoryList() as Promise<StockHistoryListResp>;
  }
  return activityHttp.post<StockHistoryListResp>({
    ...getActivityReqUrl(Api.STOCK_HISTORY_LIST),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

// ===== 新增的积分管理接口 =====

/**
 * 发放积分
 */
export function grantGuildPoints(data: GrantGuildPointsReq) {
  if (urlParams.myMock) {
    return mock.grantGuildPoints(data) as Promise<void>;
  }
  return activityHttp.post<void>({
    ...getActivityReqUrl(Api.GRANT_GUILD_POINTS),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 扣除积分
 */
export function deductGuildPoints(data: DeductGuildPointsReq) {
  if (urlParams.myMock) {
    return mock.deductGuildPoints(data) as Promise<void>;
  }
  return activityHttp.post<void>({
    ...getActivityReqUrl(Api.DEDUCT_GUILD_POINTS),
    data: {
      ...data,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 查询积分变动记录
 */
export function listPointOperations(params?: ListPointOperationsReq) {
  if (urlParams.myMock) {
    return mock.listPointOperations(params) as Promise<ListPointOperationsRes>;
  }
  return activityHttp.post<ListPointOperationsRes>({
    ...getActivityReqUrl(Api.LIST_POINT_OPERATIONS),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 导出积分变动记录
 */
export function exportPointOperations(params?: ListPointOperationsReq) {
  if (urlParams.myMock) {
    return mock.exportPointOperations(params) as Promise<ExportPointOperationsRes>;
  }
  return activityHttp.post<ExportPointOperationsRes>({
    ...getActivityReqUrl(Api.EXPORT_POINT_OPERATIONS),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 公会剩余积分排行榜
 */
export function listGuildPointInventory(params?: ListGuildPointInventoryReq) {
  if (urlParams.myMock) {
    return mock.listGuildPointInventory(params) as Promise<ListGuildPointInventoryRes>;
  }
  return activityHttp.post<ListGuildPointInventoryRes>({
    ...getActivityReqUrl(Api.LIST_GUILD_POINT_INVENTORY),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 导出公会剩余积分排行榜
 */
export function exportGuildPointInventory(params?: ListGuildPointInventoryReq) {
  if (urlParams.myMock) {
    return mock.exportGuildPointInventory(params) as Promise<ExportGuildPointInventoryRes>;
  }
  return activityHttp.post<ExportGuildPointInventoryRes>({
    ...getActivityReqUrl(Api.EXPORT_GUILD_POINT_INVENTORY),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 获取我的公会积分
 */
export function getMyGuildPoint(uid: number) {
  if (urlParams.myMock) {
    return mock.getMyGuildPoint(uid) as Promise<GetMyGuildPointRes>;
  }
  return activityHttp.post<GetMyGuildPointRes>({
    ...getActivityReqUrl(Api.GET_MY_GUILD_POINT),
    data: {
      uid,
      operatorName: getUserInfo.value,
    },
  });
}

/**
 * 获取积分记录列表
 */
export function getPointRecords(params: GetPointRecordsReq) {
  if (urlParams.myMock) {
    return mock.getPointRecords(params) as Promise<GetPointRecordsRes>;
  }
  return activityHttp.post<GetPointRecordsRes>({
    ...getActivityReqUrl(Api.GET_POINT_RECORDS),
    params: {
      ...params,
      operatorName: getUserInfo.value,
    },
  });
}
