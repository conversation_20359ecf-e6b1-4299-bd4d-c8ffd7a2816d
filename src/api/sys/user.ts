import {
  LoginParams,
  FeishuLoginParams,
  LoginResultModel,
  GetUserInfoModel,
} from './model/userModel';
import { ErrorMessageMode } from '#/axios';
import { createAxios } from '@/utils/http/axios';
import { getUrlENV } from '@/utils/env';
import url from '@/utils/url';

const nodePath = 'operation-platform';
const env = getUrlENV();

const defHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common('//testing-tt-web-tc.ttyuyin.com/common-testing')[env]}${/* env === 'dev' ? '' :  */ `/${nodePath}`}`,
    // apiUrl: `${url.node_common()['testing']}/${nodePath}`,
    urlPrefix: '/system.System/',
  },
});

enum Api {
  Login = 'login',
  LoginFeishu = 'loginFeishu',
  GetUserInfo = 'getUserInfo',
  GetPermCode = 'getPermCode',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
// 飞书登录
export function loginFeishuApi(params: FeishuLoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.LoginFeishu,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(params?: any) {
  return defHttp.post<GetUserInfoModel>(
    { url: Api.GetUserInfo, params },
    { errorMessageMode: 'none' },
  );
}

export function getPermCode() {
  return defHttp.post<string[]>({ url: Api.GetPermCode });
}

// export function doLogout() {
//   return defHttp.get({ url: Api.Logout });
// }

// export function testRetry() {
//   return defHttp.get(
//     { url: Api.TestRetry },
//     {
//       retryRequest: {
//         isOpenRetry: true,
//         count: 5,
//         waitTime: 1000,
//       },
//     },
//   );
// }
