import { UploadApiResult } from './model/uploadModel';
import { createAxios } from '@/utils/http/axios';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';
import { formatTime } from '@/utils';

const defHttp = createAxios({
  requestOptions: {
    apiUrl: 'https://obs.52tt.com',
    urlPrefix: '',
  },
});

/**
 * @description: Upload interface
 */
export function uploadApi(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
  token?: string,
) {
  if (!params.data?.key) {
    const extIndex = params.file.name.lastIndexOf('.');
    let ext = '';
    ext = extIndex !== -1 ? params.file.name.substring(extIndex + 1) : '';
    params.data = params.data || {};
    // 生成文件路径
    params.data.key = `web/user-info-config-admin/${formatTime(Date.now() / 1000, 'yyyyMMddhhmmss_S')}${Math.random().toString().substring(2, 5)}.${ext}`;
  }

  return defHttp.uploadFile<UploadApiResult>(
    {
      url: 'https://obs.52tt.com/object/',
      onUploadProgress,
      requestOptions: {
        withToken: false,
        isTransformResponse: false,
      },
      headers: {
        Authorization: `OBS ${token}`,
      },
    },
    params,
  );
}
