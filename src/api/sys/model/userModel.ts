import { PermissionEnum } from '@/enums/permissionEnum';

/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  username: string;
  password: string;
}

export interface FeishuLoginParams {
  larkCode: string;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  // userId: string | number;
  token: string;
  // roles: RoleInfo[];
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  // 角色
  roles: RoleInfo[];
  // 权限菜单codes
  permCodes: PermissionEnum[];
  // 用户id
  userId: string | number;
  // 用户名
  username: string;
  // 用户首页路径
  homePath?: string;
  // // 真实名字
  // realName: string;
  // // 头像
  // avatar: string;
  // // 介绍
  // desc?: string;
}
