import { getUrlENV } from '@/utils/env';
import { createAxios } from '@/utils/http/axios';
import url from '@/utils/url';

const nodePath = 'nconfig-v2/';
const env = getUrlENV();
const activityUserGroupPath = 'activity-user-group';

const NODE_BELUGA_URL = {
  prod: '//node-unify.52tt.com/beluga-production',
  gray: '//node-unify.52tt.com/beluga-testing',
  testing: '//testing-tt-web-tc.ttyuyin.com/beluga-testing',
  dev: '//testing-tt-web-tc.ttyuyin.com/beluga-testing',
};
const NODE_ACTIVITY_URL = {
  prod: '//node-unify.52tt.com/activity-production',
  gray: '//node-unify.52tt.com/activity-testing',
  testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing',
  dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing',
};
const defHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common()[env]}${env === 'dev' ? `/${nodePath}` : `/${nodePath}`}`,
    urlPrefix: 'manager.Manager/',
  },
});
let defHttpInstance = null;
const createDefHttpInstance = (belugaId) => {
  if (defHttpInstance) return defHttpInstance;
  const projectId = belugaId.includes('-') ? belugaId : `${belugaId}-puzzle-sign`;
  defHttpInstance = createAxios({
    requestOptions: {
      apiUrl: `${NODE_ACTIVITY_URL[env]}/${projectId}`,
      urlPrefix: '/activity.Activity/',
    },
  });
  return defHttpInstance;
};
const defOperaionHttp = createAxios({
  requestOptions: {
    apiUrl: `${url.node_common('//testing-tt-web-tc.ttyuyin.com/common-testing')[env]}${/* env === 'dev' ? '' : */ `/${activityUserGroupPath}`}`,
    urlPrefix: '/manager.Manager/',
  },
});
let defHttpBelugaInstance = null;
const createAxiosBeluga = (belugaId) => {
  if (defHttpBelugaInstance) return defHttpBelugaInstance;
  const projectId = belugaId.includes('-') ? belugaId : `${belugaId}-puzzle-sign`;
  defHttpBelugaInstance = createAxios({
    requestOptions: {
      apiUrl: `${NODE_BELUGA_URL[env]}/${projectId}`,
      urlPrefix: '/activity.Activity/',
    },
  });
  return defHttpBelugaInstance;
};

enum Api {
  getActList = 'getActList', // 获取活动列表
  getVersions = 'getVersions', // 获取历史版本
  getOperateLog = 'getOperateLog', // 获取操作历史
  saveSchema = 'saveSchema', // 保存schema
  saveConfig = 'saveConfig', // 保存配置
  createAct = 'createAct', // 新建活动
  publishConfig = 'publishConfig', // 发布配置
  getActDetail = 'getActDetail', // 获取活动详情
  getSheetData = 'getSheetData', // 获取飞书表单数据
  nconfigCheck = 'nconfigCheck', // 配置检查
  getGroupList = 'getGroupList', // 获取人群包列表
  setGroupList = 'setGroupList', // 新建 & 更新人群包信息
  getGroupWhite = 'getGroupWhite', // 获取人群包黑白名单
  setGroupWhite = 'setGroupWhite', // 新建 & 更新人群包黑白名单
  check = 'check', // 检查人群包
}

export const getActList = (params: any) => defHttp.post<any>({ url: Api.getActList, params });

export const getVersions = (params: any) => defHttp.post<any>({ url: Api.getVersions, params });

export const getOperateLog = (params: any) => defHttp.post<any>({ url: Api.getOperateLog, params });

export const saveSchema = (params: any) => defHttp.post<any>({ url: Api.saveSchema, params });

export const saveConfig = (params: any) => defHttp.post<any>({ url: Api.saveConfig, params });

export const createAct = (params: any) => defHttp.post<any>({ url: Api.createAct, params });

export const publishConfig = (params: any) => defHttp.post<any>({ url: Api.publishConfig, params });

export const getActDetail = (params: any) => defHttp.post<any>({ url: Api.getActDetail, params });

export const getSheetData = (params: any) => defHttp.post<any>({ url: Api.getSheetData, params });
export const nconfigCheck = (params: any, belugaId) =>
  createDefHttpInstance(belugaId).post<any>({ url: Api.nconfigCheck, params });
export const nconfigCheckBeluga = (params: any, belugaId) =>
  createAxiosBeluga(belugaId).post<any>({ url: Api.nconfigCheck, params });

export const getGroupList = (params: any) =>
  defOperaionHttp.post<any>({ url: Api.getGroupList, params });
export const setGroupList = (params: any) =>
  defOperaionHttp.post<any>({ url: Api.setGroupList, params });
export const getGroupWhite = (params: any) =>
  defOperaionHttp.post<any>({ url: Api.getGroupWhite, params });
export const setGroupWhite = (params: any) =>
  defOperaionHttp.post<any>({ url: Api.setGroupWhite, params });
export const check = (params: any) => defOperaionHttp.post<any>({ url: Api.check, params });
