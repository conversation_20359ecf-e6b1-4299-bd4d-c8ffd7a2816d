import { BasicColumn, FormSchema } from '@/components/Table';

const REWARD_TYPE = {
  // 王者
  WZ: 'WZ',
  // 和平精英
  CJ: 'CJ',
  // 实物
  ENTITY: 'ENTITY',
  // 现金
  CASH: 'CASH',
};
enum REWARD_NAME {
  // 王者
  WZ = '王者荣耀',
  // 和平精英
  CJ = '和平精英',
  // 实物
  ENTITY = '实物',
  // 现金
  CASH = '现金',
}

export const columns: BasicColumn[] = [
  {
    title: '兑奖种类',
    dataIndex: 'type',
    width: 120,
    fixed: 'left',
    // filters: [
    //   { text: REWARD_NAME.WZ, value: REWARD_NAME.WZ },
    //   { text: REWARD_NAME.CJ, value: REWARD_NAME.CJ },
    //   { text: REWARD_NAME.ENTITY, value: REWARD_NAME.ENTITY },
    //   { text: REWARD_NAME.CASH, value: REWARD_NAME.CASH },
    // ],
    // onFilter: (value, record) => record.type === value,
  },
  {
    title: '兑奖明细',
    dataIndex: 'describe',
    width: 120,
    fixed: 'left',
  },
  {
    title: '用户TTID',
    dataIndex: 'ttid',
    width: 120,
    fixed: 'left',
  },
  {
    title: '兑奖码',
    dataIndex: 'cdkey',
    width: 120,
    fixed: 'left',
  },
  {
    title: '所属活动id',
    dataIndex: 'activityId',
    width: 120,
  },
  {
    title: '中奖时间',
    dataIndex: 'winningTime',
    width: 180,
  },
  {
    title: '是否兑奖',
    dataIndex: 'isUse',
    width: 100,
    // filters: [
    //   { text: '是', value: 'true' },
    //   { text: '否', value: 'false' },
    // ],
    // onFilter: (value, record) => (value === 'true' ? record.isUse : !record.isUse),
    customRender: ({ value }) => {
      return value ? '是' : '否';
    },
  },
  {
    title: '兑奖时间',
    dataIndex: 'useTime',
    width: 180,
    helpMessage: ['指用户上传兑奖信息的时间'],
  },
  {
    title: '游戏ID(和平)',
    dataIndex: 'gameId',
    width: 120,
  },
  {
    title: '游戏昵称(王者、和平)',
    dataIndex: 'gameName',
    width: 180,
  },
  {
    title: '中奖截图',
    dataIndex: 'gameImg',
    width: 120,
  },
  {
    title: '姓名(现金、实物)',
    dataIndex: 'name',
    width: 120,
  },
  {
    title: '电话(实物)',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '地址(实物)',
    dataIndex: 'address',
    width: 120,
  },
  {
    title: '收款账号:支付宝/微信(现金)',
    dataIndex: 'alipay',
    width: 200,
    customRender: ({ record }) => {
      return record.alipay
        ? `支付宝: ${record.alipay}`
        : record.wechat
          ? `微信: ${record.wechat}`
          : '';
    },
  },
  {
    title: '收款码(现金)',
    dataIndex: 'payCode',
    width: 180,
  },
  {
    title: '所在平台(王者、和平)',
    dataIndex: 'os',
    width: 180,
    customRender: ({ value }) => {
      return value === 'ios' ? '苹果' : value === 'android' ? '安卓' : '';
    },
  },
  {
    title: '所在区(王者、和平)',
    dataIndex: 'area',
    width: 180,
    customRender: ({ value }) => {
      return value === 'qq' ? 'QQ区' : value === 'weixin' ? '微信区' : '';
    },
  },
  {
    title: '所需英雄皮肤名字(王者)',
    dataIndex: 'hero',
    width: 180,
  },
  {
    title: '游戏内个人主页截图(王者、和平)',
    dataIndex: 'gameHomeImg',
    width: 220,
  },
  {
    title: '游戏内心愿单截图(王者)',
    dataIndex: 'gameWishImg',
    width: 180,
  },
  {
    title: '兑奖成功截图',
    dataIndex: 'useImg',
    width: 180,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    edit: true,
    // 默认必填校验
    editRule: true,
    // editable: true,
    ellipsis: false,
    width: 200,
    fixed: 'right',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'cdkey',
    label: '兑奖码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'ttid',
    label: '用户TTID',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    colProps: { span: 6 },
    field: '[startTime, endTime]',
    label: '时间段',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    colProps: { span: 6 },
    field: 'type',
    label: '兑奖种类',
    component: 'Select',
    componentProps: {
      placeholder: '请选择兑奖种类',
      options: [
        { label: REWARD_NAME.WZ, value: REWARD_TYPE.WZ },
        { label: REWARD_NAME.CJ, value: REWARD_TYPE.CJ },
        { label: REWARD_NAME.ENTITY, value: REWARD_TYPE.ENTITY },
        { label: REWARD_NAME.CASH, value: REWARD_TYPE.CASH },
      ],
    },
  },
  {
    field: 'describe',
    label: '兑奖明细',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    colProps: { span: 6 },
    field: 'isUse',
    label: '是否兑奖',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' },
      ],
    },
  },
  {
    field: 'activityId',
    label: '所属活动id',
    component: 'Input',
    componentProps: {
      placeholder: '多个活动ID用英文逗号隔开',
    },
    colProps: { span: 6 },
  },
];
