<!-- src/views/yunying/guild-store/components/ExchangeRecord.vue -->
<template>
  <div class="exchange-record">
    <!-- 数据表格 -->
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleExport"> 导出记录 </a-button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'prizeImage'">
          <a-image
            v-if="record.prizeImage"
            :width="50"
            :height="50"
            :src="record.prizeImage"
            :preview="false"
            style="object-fit: cover; border-radius: 4px"
          />
          <span v-else class="text-gray-400">暂无图片</span>
        </template>

        <template v-if="column.key === 'exchangeTime'">
          <div>{{ formatToDateTime(record.exchangeTime) }}</div>
        </template>

        <template v-if="column.key === 'exchangeStatus'">
          <a-tag :color="getStatusColor(record.exchangeStatus)">
            {{ getStatusText(record.exchangeStatus) }}
          </a-tag>
        </template>

        <template v-if="column.key === 'pointsCost'">
          <span class="text-red-500 font-medium">{{ record.pointsCost }}积分</span>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)"> 查看详情 </a-button>
            <a-button
              v-if="record.exchangeStatus === 'pending'"
              type="link"
              size="small"
              @click="handleProcess(record)"
            >
              处理
            </a-button>
          </a-space>
        </template>
      </template>
    </BasicTable>

    <!-- 详情弹窗 -->
    <ExchangeDetailModal @register="registerModal" />
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import ExchangeDetailModal from './ExchangeDetailModal.vue';
  import { getExchangeColumns } from '../data/exchange.data';
  import { getExchangeRecordList, exportExchangeRecord } from '@/api/yunying/guild-store';

  const { createMessage } = useMessage();

  // 表格配置
  const [registerTable, { reload }] = useTable({
    title: '兑换记录列表',
    api: getExchangeRecordList,
    columns: getExchangeColumns(),
    formConfig: {
      labelWidth: 120,
      schemas: [
        {
          field: 'prizeName',
          label: '奖品名称',
          component: 'Input',
          componentProps: {
            placeholder: '请输入奖品名称',
          },
        },
        {
          field: 'guildName',
          label: '公会名称',
          component: 'Input',
          componentProps: {
            placeholder: '请输入公会名称',
          },
        },
        {
          field: 'exchangeStatus',
          label: '兑换状态',
          component: 'Select',
          componentProps: {
            placeholder: '请选择兑换状态',
            options: [
              { label: '待处理', value: 'pending' },
              { label: '已完成', value: 'completed' },
              { label: '已取消', value: 'cancelled' },
            ],
          },
        },
        {
          field: 'exchangeTime',
          label: '兑换时间',
          component: 'RangePicker',
          componentProps: {
            showTime: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            placeholder: ['开始时间', '结束时间'],
          },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  // 弹窗配置
  const [registerModal, { openModal }] = useModal();

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      pending: 'orange',
      completed: 'green',
      cancelled: 'red',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      pending: '待处理',
      completed: '已完成',
      cancelled: '已取消',
    };
    return textMap[status] || '未知';
  }

  // 查看详情
  function handleView(record: any) {
    openModal(true, {
      record,
    });
  }

  // 处理兑换
  function handleProcess(record: any) {
    // 这里可以打开处理弹窗或直接处理
    createMessage.info('处理功能待实现');
  }

  // 导出记录
  async function handleExport() {
    try {
      await exportExchangeRecord();
      createMessage.success('导出成功');
    } catch (error) {
      createMessage.error('导出失败');
    }
  }
</script>

<style lang="less" scoped>
  .exchange-record {
    // 样式可以根据需要添加
  }
</style>
