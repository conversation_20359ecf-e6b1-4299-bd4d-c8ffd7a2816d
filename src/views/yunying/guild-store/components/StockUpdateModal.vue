<!-- src/views/yunying/guild-store/components/StockUpdateModal.vue -->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="更新库存"
    :width="500"
    @ok="handleSubmit"
  >
    <div class="stock-update">
      <!-- 奖品信息 -->
      <div class="prize-info">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="奖品名称">
            {{ prizeData.prizeName }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            <span
              :class="{ 'text-red-500': prizeData.remainingStock <= prizeData.lowStockThreshold }"
            >
              {{ prizeData.remainingStock === -1 ? '不限制' : prizeData.remainingStock }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="已兑换数量">
            {{ prizeData.exchangedCount }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 更新表单 -->
      <BasicForm @register="registerForm" />
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { updateStock } from '@/api/yunying/guild-store';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const prizeData = ref<any>({});

  const [registerForm, { resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: [
      {
        field: 'updateType',
        label: '更新类型',
        component: 'RadioGroup',
        defaultValue: 'set',
        componentProps: {
          options: [
            { label: '设置库存', value: 'set' },
            { label: '增加库存', value: 'add' },
            { label: '减少库存', value: 'subtract' },
          ],
        },
        rules: [
          {
            required: true,
            message: '请选择更新类型',
          },
        ],
      },
      {
        field: 'stockAmount',
        label: '库存数量',
        component: 'InputNumber',
        componentProps: {
          min: -1,
          placeholder: '请输入库存数量（-1表示不限制）',
        },
        helpMessage: '设置为-1时表示不限制库存',
        rules: [
          {
            required: true,
            message: '请输入库存数量',
          },
        ],
      },
      {
        field: 'lowStockThreshold',
        label: '库存预警阈值',
        component: 'InputNumber',
        componentProps: {
          min: 0,
          placeholder: '请输入预警阈值',
        },
        helpMessage: '当库存低于此值时会显示预警',
      },
      {
        field: 'remark',
        label: '更新说明',
        component: 'InputTextArea',
        componentProps: {
          placeholder: '请输入更新说明',
          rows: 3,
        },
      },
    ],
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    prizeData.value = data.record || {};
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await updateStock({
        prizeId: prizeData.value.id,
        ...values,
      });

      createMessage.success('库存更新成功');
      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error('库存更新失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  .stock-update {
    .prize-info {
      margin-bottom: 24px;
    }
  }
</style>
