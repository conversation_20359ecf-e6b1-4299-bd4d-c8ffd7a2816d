<!-- src/views/yunying/guild-store/components/InventoryManage.vue -->
<template>
  <div class="inventory-manage">
    <!-- 数据表格 -->
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'prizeImage'">
          <a-image
            v-if="record.prizeImage"
            :width="50"
            :height="50"
            :src="record.prizeImage"
            :preview="false"
            style="object-fit: cover; border-radius: 4px"
          />
          <span v-else class="text-gray-400">暂无图片</span>
        </template>

        <template v-if="column.key === 'totalStock'">
          <span :class="{ 'text-red-500': record.totalStock <= record.lowStockThreshold }">
            {{ record.totalStock === -1 ? '不限制' : record.totalStock }}
          </span>
        </template>

        <template v-if="column.key === 'remainingStock'">
          <span :class="{ 'text-red-500': record.remainingStock <= record.lowStockThreshold }">
            {{ record.remainingStock === -1 ? '不限制' : record.remainingStock }}
          </span>
        </template>

        <template v-if="column.key === 'exchangedCount'">
          <a-tag color="blue">{{ record.exchangedCount }}</a-tag>
        </template>

        <template v-if="column.key === 'stockStatus'">
          <a-tag :color="getStockStatusColor(record.stockStatus)">
            {{ getStockStatusText(record.stockStatus) }}
          </a-tag>
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import StockUpdateModal from './StockUpdateModal.vue';
  import StockHistoryModal from './StockHistoryModal.vue';
  import { getInventoryColumns } from '../data/inventory.data';
  import { getInventoryList } from '@/api/yunying/guild-store/index';

  const { createMessage } = useMessage();

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: getInventoryList,
    columns: getInventoryColumns(),
    formConfig: {
      labelWidth: 120,
      schemas: [
        // 日期
        {
          field: 'exchangeTime',
          label: '兑换时间',
          component: 'RangePicker',
          componentProps: {
            showTime: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            placeholder: ['开始时间', '结束时间'],
          },
        },
        // 工会ID
        {
          field: 'guildId',
          label: '工会ID',
          component: 'Input',
          componentProps: {
            placeholder: '请输入工会ID',
          },
        },
        // 会长TTID
        {
          field: 'ttid',
          label: '会长TTID',
          component: 'Input',
          componentProps: {
            placeholder: '请输会长TTID',
          },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    bordered: true,
  });

  // 弹窗配置
  const [registerModal, { openModal }] = useModal();
  const [registerHistoryModal, { openModal: openHistoryModal }] = useModal();

  // 获取库存状态颜色
  function getStockStatusColor(status: string) {
    const colorMap = {
      normal: 'green',
      low: 'orange',
      out: 'red',
    };
    return colorMap[status] || 'default';
  }

  // 获取库存状态文本
  function getStockStatusText(status: string) {
    const textMap = {
      normal: '正常',
      low: '库存不足',
      out: '缺货',
    };
    return textMap[status] || '未知';
  }

  // 批量更新库存
  function handleBatchUpdate() {
    createMessage.info('批量更新功能待实现');
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped>
  .inventory-manage {
    // 样式可以根据需要添加
  }
</style>
