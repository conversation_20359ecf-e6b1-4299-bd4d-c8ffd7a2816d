<!-- src/views/yunying/guild-store/components/PrizeModal.vue -->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="800"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { prizeFormSchema } from '../data/prize.data';
  import { createPrize, updatePrize } from '@/api/yunying/guild-store';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ImageUpload } from '@/components/Upload';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: prizeFormSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.prizeId;
      setFieldsValue({
        ...data.record,
      });
    }
    if (data.record.prizeId) {
      setFieldsValue({
        ...data.record,
      });
    }

    // 如果是只读模式，禁用所有表单项
    if (data?.readonly) {
      updateSchema(
        prizeFormSchema.map((schema) => ({
          field: schema.field,
          componentProps: { disabled: true },
        })),
      );
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增奖品' : '编辑奖品'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 处理时间范围格式
      if (values.displayTime && Array.isArray(values.displayTime)) {
        values.displayTime = values.displayTime.map((time) =>
          typeof time === 'string' ? time : time.format('YYYY-MM-DD HH:mm:ss'),
        );
      }

      if (unref(isUpdate)) {
        await updatePrize({ ...values, prizeId: rowId.value });
        createMessage.success('更新成功');
      } else {
        await createPrize(values);
        createMessage.success('新增成功');
      }

      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error(unref(isUpdate) ? '更新失败' : '新增失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
