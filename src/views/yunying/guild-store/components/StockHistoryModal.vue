<!-- src/views/yunying/guild-store/components/StockHistoryModal.vue -->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="库存变更记录"
    :width="800"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <div class="stock-history">
      <!-- 奖品信息 -->
      <div class="prize-info">
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="奖品名称">
            {{ prizeData.prizeName }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            {{ prizeData.remainingStock === -1 ? '不限制' : prizeData.remainingStock }}
          </a-descriptions-item>
          <a-descriptions-item label="已兑换数量">
            {{ prizeData.exchangedCount }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 变更记录表格 -->
      <BasicTable @register="registerTable" :canResize="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'changeType'">
            <a-tag :color="getChangeTypeColor(record.changeType)">
              {{ getChangeTypeText(record.changeType) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'changeAmount'">
            <span :class="getChangeAmountClass(record.changeType)">
              {{ formatChangeAmount(record.changeType, record.changeAmount) }}
            </span>
          </template>

          <template v-if="column.key === 'createTime'">
            {{ formatToDateTime(record.createTime) }}
          </template>
        </template>
      </BasicTable>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicTable, useTable } from '@/components/Table';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { getStockHistoryList } from '@/api/yunying/guild-store';

  const prizeData = ref<any>({});

  // 表格配置
  const [registerTable] = useTable({
    api: (params) => getStockHistoryList({ ...params, prizeId: prizeData.value.id }),
    columns: [
      {
        title: '变更类型',
        dataIndex: 'changeType',
        width: 100,
      },
      {
        title: '变更数量',
        dataIndex: 'changeAmount',
        width: 100,
      },
      {
        title: '变更前库存',
        dataIndex: 'beforeStock',
        width: 100,
      },
      {
        title: '变更后库存',
        dataIndex: 'afterStock',
        width: 100,
      },
      {
        title: '操作人',
        dataIndex: 'operatorName',
        width: 100,
      },
      {
        title: '变更时间',
        dataIndex: 'createTime',
        width: 150,
      },
      {
        title: '说明',
        dataIndex: 'remark',
        width: 200,
        ellipsis: true,
      },
    ],
    pagination: {
      pageSize: 10,
    },
    showTableSetting: false,
    bordered: true,
  });

  const [registerModal] = useModalInner(async (data) => {
    prizeData.value = data.record || {};
  });

  // 获取变更类型颜色
  function getChangeTypeColor(type: string) {
    const colorMap = {
      set: 'blue',
      add: 'green',
      subtract: 'orange',
      exchange: 'red',
    };
    return colorMap[type] || 'default';
  }

  // 获取变更类型文本
  function getChangeTypeText(type: string) {
    const textMap = {
      set: '设置库存',
      add: '增加库存',
      subtract: '减少库存',
      exchange: '兑换消耗',
    };
    return textMap[type] || '未知';
  }

  // 获取变更数量样式类
  function getChangeAmountClass(type: string) {
    if (type === 'add') return 'text-green-500';
    if (type === 'subtract' || type === 'exchange') return 'text-red-500';
    return '';
  }

  // 格式化变更数量
  function formatChangeAmount(type: string, amount: number) {
    if (type === 'add') return `+${amount}`;
    if (type === 'subtract' || type === 'exchange') return `-${amount}`;
    return amount.toString();
  }
</script>

<style lang="less" scoped>
  .stock-history {
    .prize-info {
      margin-bottom: 16px;
    }
  }
</style>
