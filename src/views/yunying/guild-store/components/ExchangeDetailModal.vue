<!-- src/views/yunying/guild-store/components/ExchangeDetailModal.vue -->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="兑换详情"
    :width="600"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <div class="exchange-detail">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="兑换ID">
          {{ detailData.id }}
        </a-descriptions-item>
        <a-descriptions-item label="兑换状态">
          <a-tag :color="getStatusColor(detailData.exchangeStatus)">
            {{ getStatusText(detailData.exchangeStatus) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="奖品名称" :span="2">
          {{ detailData.prizeName }}
        </a-descriptions-item>
        <a-descriptions-item label="奖品图片" :span="2">
          <a-image
            v-if="detailData.prizeImage"
            :width="100"
            :height="100"
            :src="detailData.prizeImage"
            style="object-fit: cover; border-radius: 4px"
          />
          <span v-else class="text-gray-400">暂无图片</span>
        </a-descriptions-item>
        <a-descriptions-item label="公会名称">
          {{ detailData.guildName }}
        </a-descriptions-item>
        <a-descriptions-item label="用户昵称">
          {{ detailData.userName }}
        </a-descriptions-item>
        <a-descriptions-item label="用户ID">
          {{ detailData.userId }}
        </a-descriptions-item>
        <a-descriptions-item label="消耗积分">
          <span class="text-red-500 font-medium">{{ detailData.pointsCost }}积分</span>
        </a-descriptions-item>
        <a-descriptions-item label="兑换时间" :span="2">
          {{ formatToDateTime(detailData.exchangeTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="联系方式" :span="2">
          {{ detailData.contactInfo || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="收货地址" :span="2">
          {{ detailData.shippingAddress || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ detailData.remark || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="detailData.exchangeStatus === 'pending'">
        <a-space>
          <a-button type="primary" @click="handleComplete"> 标记完成 </a-button>
          <a-button danger @click="handleCancel"> 取消兑换 </a-button>
        </a-space>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { updateExchangeStatus } from '@/api/yunying/guild-store';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const detailData = ref<any>({});

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    detailData.value = data.record || {};
  });

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      pending: 'orange',
      completed: 'green',
      cancelled: 'red',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      pending: '待处理',
      completed: '已完成',
      cancelled: '已取消',
    };
    return textMap[status] || '未知';
  }

  // 标记完成
  async function handleComplete() {
    try {
      await updateExchangeStatus(detailData.value.id, 'completed');
      createMessage.success('操作成功');
      detailData.value.exchangeStatus = 'completed';
      emit('success');
    } catch (error) {
      createMessage.error('操作失败');
    }
  }

  // 取消兑换
  async function handleCancel() {
    try {
      await updateExchangeStatus(detailData.value.id, 'cancelled');
      createMessage.success('操作成功');
      detailData.value.exchangeStatus = 'cancelled';
      emit('success');
    } catch (error) {
      createMessage.error('操作失败');
    }
  }
</script>

<style lang="less" scoped>
  .exchange-detail {
    .action-buttons {
      margin-top: 24px;
      text-align: center;
    }
  }
</style>
