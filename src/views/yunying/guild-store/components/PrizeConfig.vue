<!-- src/views/yunying/guild-store/components/PrizeConfig.vue -->
<template>
  <div class="prize-config">
    <div class="action-bar">
      <a-space>
        <a-button type="primary" @click="handleAdd"> 新增 </a-button>
        <!-- <a-button @click="handleEdit" :disabled="!selectedRowKeys.length"> 修改 </a-button>
        <a-button danger @click="handleDelete" :disabled="!selectedRowKeys.length"> 删除 </a-button> -->
        <a-button type="primary" @click="handleEdit"> 保存 </a-button>
        <a-button type="warning" @click="handlePush"> 推送 </a-button>
      </a-space>
      <div class="tips">
        以下配置点击【推送】后将更新至积分商城活动页面,请谨慎作!(未推送配置不生效)
      </div>
    </div>

    <!-- 数据表格 -->
    <BasicTable @register="registerTable" @row-click="handleRowClick">
      <template #toolbar>
        <a-button type="primary" @click="handleAdd"> 新增奖品 </a-button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'prizeImage'">
          <a-image
            v-if="record.prizeImage"
            :width="50"
            :height="50"
            :src="record.prizeImage"
            :preview="false"
            style="object-fit: cover; border-radius: 4px"
          />
          <span v-else class="text-gray-400">暂无图片</span>
        </template>

        <template v-if="column.key === 'displayTime'">
          <div v-if="record.displayTime">
            {{ formatTimeRange(record.displayTime) }}
          </div>
          <span v-else>-</span>
        </template>

        <template v-if="column.key === 'exchangeCondition'">
          <a-tag color="blue">{{ record.exchangeCondition }}积分</a-tag>
        </template>

        <template v-if="column.key === 'singleGuildLimit'">
          <span>{{ record.singleGuildLimit === -1 ? '不限制' : record.singleGuildLimit }}</span>
        </template>

        <template v-if="column.key === 'totalSiteLimit'">
          <span>{{ record.totalSiteLimit === -1 ? '不限制' : record.totalSiteLimit }}</span>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            <a-button type="link" size="small" @click="handleView(record)"> 修改 </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </BasicTable>

    <!-- 新增/编辑弹窗 -->
    <PrizeModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import PrizeModal from './PrizeModal.vue';
  import { getPrizeColumns } from '../data/prize.data';
  import { getPrizeList, deletePrize } from '@/api/yunying/guild-store';

  const { createMessage } = useMessage();

  // 选中的行
  const selectedRowKeys = ref<string[]>([]);
  const selectedRows = ref<any[]>([]);

  // 行选择配置
  const rowSelection = reactive({
    type: 'checkbox',
    selectedRowKeys,
    onChange: (keys: string[], rows: any[]) => {
      selectedRowKeys.value = keys;
      selectedRows.value = rows;
    },
  });

  // 表格配置
  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '奖品配置列表',
    api: getPrizeList,
    columns: getPrizeColumns(),
    formConfig: {
      labelWidth: 120,
      schemas: [
        {
          field: 'prizeName',
          label: '奖品名称',
          component: 'Input',
          componentProps: {
            placeholder: '请输入奖品名称',
          },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  // 弹窗配置
  const [registerModal, { openModal }] = useModal();

  // 格式化时间范围
  function formatTimeRange(timeRange: string[] | string) {
    if (Array.isArray(timeRange) && timeRange.length === 2) {
      return `${timeRange[0]} ~ ${timeRange[1]}`;
    }
    return timeRange || '-';
  }

  // 新增
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }

  // 编辑
  function handleEdit(record?: any) {
    if (record) {
      openModal(true, {
        record,
        isUpdate: true,
      });
    } else if (selectedRows.value.length > 0) {
      openModal(true, {
        record: selectedRows.value[0],
        isUpdate: true,
      });
    }
  }

  // 查看
  function handleView(record: any) {
    openModal(true, {
      record,
      isUpdate: true,
      readonly: true,
    });
  }

  // 删除
  async function handleDelete(record?: any) {
    const ids = record ? [record.id] : selectedRowKeys.value;
    if (ids.length === 0) {
      createMessage.warning('请选择要删除的数据');
      return;
    }

    try {
      await deletePrize(ids);
      createMessage.success('删除成功');
      reload();
      selectedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  // 行点击
  function handleRowClick(record: any) {
    const key = record.id;
    if (selectedRowKeys.value.includes(key)) {
      selectedRowKeys.value = selectedRowKeys.value.filter((item) => item !== key);
      selectedRows.value = selectedRows.value.filter((item) => item.id !== key);
    } else {
      selectedRowKeys.value.push(key);
      selectedRows.value.push(record);
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped>
  .prize-config {
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 6px;

      .tips {
        color: red;
        font-size: 14px;
        max-width: 600px;
      }
    }
  }
</style>
