<!-- src/views/yunying/guild-store/components/PrizeConfig.vue -->
<template>
  <div class="prize-config">
    <!-- 数据表格 -->
    <BasicTable @register="registerTable" @row-click="handleRowClick">
      <template #toolbar>
        <div class="action-bar">
          <a-button type="primary" class="mr-2" c @click="handleAdd"> 新增 </a-button>
          <a-button type="primary" class="mr-2" danger @click="pushData"> 推送 </a-button>
          <div class="tips">
            以下配置点击【推送】后将更新至积分商城活动页面,请谨慎作!(未推送配置不生效)
          </div>
        </div>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'prizeImageUrl'">
          <ImgPreview
            class="object-cover rounded-md"
            v-if="record.prizeImageUrl"
            readonly
            :modelValue="[record.prizeImageUrl]"
            :preview="true"
          />
          <span v-else class="text-gray-400">暂无图片</span>
        </template>

        <template v-if="column.key === 'redemptionPoints'">
          <a-tag color="blue">{{ record.redemptionPoints }}</a-tag>
        </template>

        <template v-if="column.key === 'guildRedemptionLimit'">
          <span>{{
            record.guildRedemptionLimit === -1 ? '不限制' : record.guildRedemptionLimit
          }}</span>
        </template>

        <template v-if="column.key === 'siteRedemptionLimit'">
          <span>{{
            record.siteRedemptionLimit === -1 ? '不限制' : record.siteRedemptionLimit
          }}</span>
        </template>

        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>

    <!-- 新增/编辑弹窗 -->
    <PrizeModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Image } from 'ant-design-vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import PrizeModal from './PrizeModal.vue';
  import { getPrizeColumns } from '../data/prize.data';
  import { getPrizeList, deletePrize, pushPrize } from '@/api/yunying/guild-store/index';
  import ImgPreview from '@/components/ImgPerview/index.vue';

  const { createMessage, createConfirm } = useMessage();

  // 选中的行
  const selectedRowKeys = ref<string[]>([]);
  const selectedRows = ref<any[]>([]);

  // 行选择配置
  const rowSelection = reactive({
    type: 'checkbox',
    selectedRowKeys,
    onChange: (keys: string[], rows: any[]) => {
      selectedRowKeys.value = keys;
      selectedRows.value = rows;
    },
  });

  // 表格配置
  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '',
    api: getPrizeList,
    columns: getPrizeColumns(),
    formConfig: {
      labelWidth: 120,
      schemas: [
        {
          field: 'prizeName',
          label: '奖品名称',
          component: 'Input',
          componentProps: {
            placeholder: '请输入奖品名称',
          },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    showIndexColumn: false,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  const loading = ref(false);
  // 推送
  async function pushData() {
    if (loading.value) return;
    loading.value = true;
    try {
      createConfirm({
        iconType: 'warning',
        title: '推送',
        content: `是否确认操作？`,
        onOk: async () => {
          const res = await pushPrize({});
          createMessage.success('推送成功');
        },
      });
    } finally {
      loading.value = false;
    }
  }

  // 弹窗配置
  const [registerModal, { openModal }] = useModal();

  // 新增
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }

  // 编辑
  function handleEdit(record?: any) {
    if (record) {
      openModal(true, {
        record,
        isUpdate: true,
      });
    } else if (selectedRows.value.length > 0) {
      openModal(true, {
        record: selectedRows.value[0],
        isUpdate: true,
      });
    }
  }

  // 复制
  function handleCopy(record: any) {
    openModal(true, {
      record,
      isUpdate: false,
    });
  }

  // 删除
  async function handleDelete(record?: any) {
    const ids = record ? [record.id] : selectedRowKeys.value;
    if (ids.length === 0) {
      createMessage.warning('请选择要删除的数据');
      return;
    }

    try {
      await deletePrize(ids);
      createMessage.success('删除成功');
      reload();
      selectedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  // 行点击
  function handleRowClick(record: any) {
    const key = record.id;
    if (selectedRowKeys.value.includes(key)) {
      selectedRowKeys.value = selectedRowKeys.value.filter((item) => item !== key);
      selectedRows.value = selectedRows.value.filter((item) => item.id !== key);
    } else {
      selectedRowKeys.value.push(key);
      selectedRows.value.push(record);
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped>
  .prize-config {
    .action-bar {
      display: flex;
      align-items: center;
      margin-right: auto;
      background: #f8f9fa;

      .tips {
        max-width: 600px;
        color: red;
        font-size: 14px;
      }
    }
  }
</style>
