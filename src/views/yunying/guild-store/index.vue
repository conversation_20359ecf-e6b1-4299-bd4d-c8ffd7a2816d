<!-- src/views/yunying/guild-store/index.vue -->
<template>
  <div class="guild-store-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>公会积分商城配置</h2>
    </div>

    <!-- Tab 切换 -->
    <Tabs v-model:activeKey="activeTab" type="card" class="store-tabs">
      <tab-pane key="prize-config" tab="奖品配置">
        <PrizeConfig />
      </tab-pane>
      <tab-pane key="exchange-record" tab="兑换记录">
        <ExchangeRecord />
      </tab-pane>
      <tab-pane key="inventory-manage" tab="库存管理">
        <InventoryManage />
      </tab-pane>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Card, Select, SelectOption, Tabs, TabPane, Empty, Spin } from 'ant-design-vue';
  import PrizeConfig from './components/PrizeConfig.vue';
  import ExchangeRecord from './components/ExchangeRecord.vue';
  import InventoryManage from './components/InventoryManage.vue';

  // 当前激活的Tab
  const activeTab = ref('prize-config');
</script>

<style lang="less" scoped>
  .guild-store-container {
    padding: 16px;
    background: #f5f5f5;
    min-height: 100vh;

    .page-header {
      margin-bottom: 16px;

      h2 {
        margin: 0;
        color: #333;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .alert-info {
      margin-bottom: 16px;
    }

    .store-tabs {
      background: #fff;
      border-radius: 6px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.ant-tabs-content-holder) {
        padding-top: 16px;
      }
    }
  }
</style>
