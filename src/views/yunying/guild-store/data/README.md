# Prize Data 表单配置使用说明

## 时间字段处理

### 问题背景
原来的数据结构使用单个 `displayTime` 字段，现在改为两个独立的时间戳字段：
- `displayStartTime` - 展示开始时间（Unix 时间戳）
- `displayEndTime` - 展示结束时间（Unix 时间戳）

但表单中仍需要使用 RangePicker 组件来提供良好的用户体验。

### 解决方案

#### 1. 数据转换工具函数

```typescript
import { transformPrizeFormData } from './prize.data';

// 表单提交前转换
const submitData = transformPrizeFormData.beforeSubmit(formData);

// 表单初始化时转换
const initialData = transformPrizeFormData.afterLoad(apiData);
```

#### 2. 表单配置

```typescript
{
  field: 'displayTimeRange', // 使用临时字段名
  label: '展示时间',
  component: 'RangePicker',
  componentProps: {
    format: 'YYYY-MM-DD HH:mm:ss',
    placeholder: ['开始日期', '结束日期'],
    showTime: {
      defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
    },
    valueFormat: 'X', // Unix 时间戳格式
  },
  // 自定义默认值
  defaultValue: () => [
    dayjs().startOf('day'),
    dayjs().endOf('day'),
  ],
}
```

### 使用示例

#### 在表单组件中使用

```vue
<template>
  <BasicForm @register="registerForm" @submit="handleSubmit" />
</template>

<script setup>
import { BasicForm, useForm } from '@/components/Form';
import { prizeFormSchema, transformPrizeFormData } from './data/prize.data';

const [registerForm, { validate, setFieldsValue }] = useForm({
  schemas: prizeFormSchema,
  showActionButtonGroup: true,
  actionColOptions: {
    span: 24,
  },
});

// 表单提交
async function handleSubmit() {
  try {
    const values = await validate();
    
    // 转换数据格式
    const submitData = transformPrizeFormData.beforeSubmit(values);
    
    // 提交到后端
    await createPrize(submitData);
    
    console.log('提交的数据:', submitData);
    // 输出: { displayStartTime: 1640995200, displayEndTime: 1641081599, ... }
    
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 编辑时初始化表单数据
async function initFormData(id: string) {
  try {
    const apiData = await getPrizeDetail(id);
    
    // 转换数据格式用于表单显示
    const formData = transformPrizeFormData.afterLoad(apiData);
    
    // 设置表单值
    await setFieldsValue(formData);
    
    console.log('表单数据:', formData);
    // 输出: { displayTimeRange: [dayjs对象1, dayjs对象2], ... }
    
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}
</script>
```

#### 在列表页面中使用

```vue
<template>
  <BasicTable @register="registerTable" />
</template>

<script setup>
import { BasicTable, useTable } from '@/components/Table';
import { getPrizeColumns } from './data/prize.data';

const [registerTable] = useTable({
  api: getPrizeList,
  columns: getPrizeColumns(),
  // ... 其他配置
});
</script>
```

### 数据流转过程

#### 1. 新增奖品
```
用户选择时间范围 → RangePicker → displayTimeRange: [dayjs1, dayjs2]
                                        ↓
                            transformPrizeFormData.beforeSubmit()
                                        ↓
                    API提交: { displayStartTime: 1640995200, displayEndTime: 1641081599 }
```

#### 2. 编辑奖品
```
API返回: { displayStartTime: 1640995200, displayEndTime: 1641081599 }
                                        ↓
                            transformPrizeFormData.afterLoad()
                                        ↓
                    表单显示: displayTimeRange: [dayjs1, dayjs2] → RangePicker
```

#### 3. 列表显示
```
API数据: { displayStartTime: 1640995200, displayEndTime: 1641081599 }
                                        ↓
                                format函数转换
                                        ↓
                        显示: "2022-01-01 00:00:00 - 2022-01-01 23:59:59"
```

### 注意事项

1. **时区处理**: 所有时间都使用 Asia/Shanghai 时区
2. **数据验证**: RangePicker 有自定义验证器确保时间范围完整
3. **默认值**: 新增时默认为当天的开始和结束时间
4. **字段命名**: 表单中使用 `displayTimeRange`，API 使用 `displayStartTime` 和 `displayEndTime`
5. **数据转换**: 必须在表单提交前和数据加载后进行相应的数据转换

### 扩展功能

如果需要添加更多的时间相关验证，可以在转换函数中添加：

```typescript
beforeSubmit: (formData: any) => {
  const data = { ...formData };
  
  if (data.displayTimeRange && Array.isArray(data.displayTimeRange)) {
    const [startTime, endTime] = data.displayTimeRange;
    
    // 验证时间范围
    if (dayjs(endTime).isBefore(dayjs(startTime))) {
      throw new Error('结束时间不能早于开始时间');
    }
    
    // 验证时间不能是过去时间
    if (dayjs(startTime).isBefore(dayjs())) {
      throw new Error('开始时间不能是过去时间');
    }
    
    data.displayStartTime = dayjs(startTime).unix();
    data.displayEndTime = dayjs(endTime).unix();
    delete data.displayTimeRange;
  }
  
  return data;
}
```
