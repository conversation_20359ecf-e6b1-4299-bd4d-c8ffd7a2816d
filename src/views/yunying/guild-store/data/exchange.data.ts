// src/views/yunying/guild-store/data/exchange.data.ts
import { BasicColumn } from '@/components/Table';

// 兑换记录表格列
export function getExchangeColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '奖品图片',
      dataIndex: 'prizeImage',
      width: 100,
    },
    {
      title: '公会名称',
      dataIndex: 'guildName',
      width: 150,
    },
    {
      title: '用户昵称',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      width: 100,
    },
    {
      title: '消耗积分',
      dataIndex: 'pointsCost',
      width: 100,
      sorter: true,
    },
    {
      title: '兑换时间',
      dataIndex: 'exchangeTime',
      width: 180,
      sorter: true,
    },
    {
      title: '兑换状态',
      dataIndex: 'exchangeStatus',
      width: 100,
    },
    {
      title: '联系方式',
      dataIndex: 'contactInfo',
      width: 150,
      ellipsis: true,
    },
    {
      title: '收货地址',
      dataIndex: 'shippingAddress',
      width: 200,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      ellipsis: true,
    },
  ];
}
