// src/views/yunying/guild-store/data/prize.data.ts
import { BasicColumn, FormSchema } from '@/components/Table';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';
import { getObsToken, uploadUseImg } from '@/api/exchange';
import { uploadApi } from '@/api/sys/upload';

const _uploadApi = async (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) => {
  const { token } = await getObsToken();
  const res = await uploadApi(params, onUploadProgress, token as string);
  return res;
};
// 奖品配置表格列
export function getPrizeColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '奖品图片',
      dataIndex: 'prizeImage',
      width: 100,
    },
    {
      title: '展示权重',
      dataIndex: 'displayWeight',
      width: 100,
      sorter: true,
    },
    {
      title: '展示时间',
      dataIndex: 'displayTime',
      width: 200,
    },
    {
      title: '兑换条件',
      dataIndex: 'exchangeCondition',
      width: 120,
    },
    {
      title: '单个公会兑换限制',
      dataIndex: 'singleGuildLimit',
      width: 150,
    },
    {
      title: '全站兑换限制',
      dataIndex: 'totalSiteLimit',
      width: 130,
    },
    {
      title: '备注',
      dataIndex: 'remarkText',
      width: 200,
      ellipsis: true,
    },
  ];
}

// 奖品配置表单Schema
export const prizeFormSchema: FormSchema[] = [
  {
    field: 'prizeName',
    label: '奖品名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入奖品名称',
    },
    rules: [
      {
        required: true,
        message: '请输入奖品名称',
      },
    ],
  },
  {
    field: 'prizeImage',
    label: '奖品图片',
    component: 'ImageUpload',
    componentProps: {
      api: _uploadApi, // 需要根据项目实际情况配置上传接口
      maxNumber: 1,
      accept: ['image/*'],
      maxSize: 20,
      resultField: 'data.location',
    },
    rules: [
      {
        required: true,
        message: '请上传奖品图片',
      },
    ],
  },
  {
    field: 'displayWeight',
    label: '展示权重',
    component: 'InputNumber',
    componentProps: {
      placeholder: '权重越大，排在越前',
      min: 0,
    },
    helpMessage: '权重越大，排在越前',
    rules: [
      {
        required: true,
        message: '请输入展示权重',
      },
    ],
  },
  {
    field: 'displayTime',
    label: '展示时间',
    component: 'RangePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始时间', '结束时间'],
    },
    rules: [
      {
        required: true,
        message: '请选择展示时间',
      },
    ],
  },
  {
    field: 'exchangeCondition',
    label: '兑换条件',
    component: 'Input',
    componentProps: {
      placeholder: '单个奖品所需积分数（仅支持正数）',
    },
    helpMessage: '单个奖品所需积分数（仅支持正数）',
    rules: [
      {
        required: true,
        message: '请输入兑换条件',
      },
      {
        pattern: /^[1-9]\d*$/,
        message: '请输入正整数',
      },
    ],
  },
  {
    field: 'singleGuildLimit',
    label: '单个公会兑换限制',
    component: 'InputNumber',
    componentProps: {
      min: -1,
      placeholder: '请输入限制数量',
    },
    helpMessage: '配置为-1时，购买不限制数量；活动页面不展示文案',
    rules: [
      {
        required: true,
        message: '请输入单个公会兑换限制',
      },
    ],
  },
  {
    field: 'totalSiteLimit',
    label: '全站兑换限制',
    component: 'InputNumber',
    componentProps: {
      min: -1,
      placeholder: '请输入限制数量',
    },
    helpMessage: '配置为-1时，购买不限制数量；活动页面不展示文案',
    rules: [
      {
        required: true,
        message: '请输入全站兑换限制',
      },
    ],
  },
  {
    field: 'remarkText',
    label: '备注文案',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '兑换须知或活动规则等信息',
      rows: 4,
    },
    helpMessage: '不配置则活动页面不展示文案',
  },
];
