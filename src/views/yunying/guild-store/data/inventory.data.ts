// src/views/yunying/guild-store/data/inventory.data.ts
import { BasicColumn } from '@/components/Table';

// 库存管理表格列
export function getInventoryColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '奖品图片',
      dataIndex: 'prizeImage',
      width: 100,
    },
    {
      title: '总库存',
      dataIndex: 'totalStock',
      width: 100,
      sorter: true,
    },
    {
      title: '剩余库存',
      dataIndex: 'remainingStock',
      width: 100,
      sorter: true,
    },
    {
      title: '已兑换数量',
      dataIndex: 'exchangedCount',
      width: 120,
      sorter: true,
    },
    {
      title: '库存状态',
      dataIndex: 'stockStatus',
      width: 100,
    },
    {
      title: '库存预警阈值',
      dataIndex: 'lowStockThreshold',
      width: 120,
    },
    {
      title: '最后更新时间',
      dataIndex: 'lastUpdateTime',
      width: 180,
      sorter: true,
    },
    {
      title: '更新人',
      dataIndex: 'updatedBy',
      width: 100,
    },
  ];
}
